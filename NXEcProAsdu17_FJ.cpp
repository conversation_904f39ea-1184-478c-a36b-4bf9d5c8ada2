/**********************************************************************
* NXEcProAsdu17_FJ.cpp         author:ml      date:08/11/2025            
*---------------------------------------------------------------------
*  note: ASDU17报文转换处理实现文件 继承类(福建103)                                                            
*  
**********************************************************************/

#include "NXEcProAsdu17_FJ.h"


/**
* @brief         析构函数
* @param[in]     无 
* @param[out]    无
* @return        无
*/
TNXEcProAsdu17FJ::~TNXEcProAsdu17FJ()
{

}

/**
* @brief         构造函数 
* @param[in]     INXEcSSModelSeek * pSeekIns:模型查询实例
* @param[out]    CLogRecord * pLogRecord:日志对象指针
* @return        无
*/
TNXEcProAsdu17FJ::TNXEcProAsdu17FJ(IN INXEcSSModelSeek * pSeekIns,IN CLogRecord * pLogRecord)
	:TNXEcProAsdu17(pSeekIns,pLogRecord)
{
	m_pCommonMsg = NULL;
	// 设置类名称
	_SetLogClassName("TNXEcProAsdu17FJ");
}

/**
* @brief         根据NX通用信息及规约命令生成规约结果列表或根据通用消息生成规约命令
* @param[in]     NX_COMMON_MESSAGE * pMsg :通用信息结构指针
* @param[in][out]PRO_FRAME_BODY_LIST & lCmd:规约命令
* @param[out]    PRO_FRAME_BODY_LIST & lResult :规约信息体列表
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu17FJ::ConvertCommonMsgToPro(IN NX_COMMON_MESSAGE * pMsg,IN OUT PRO_FRAME_BODY_LIST & lCmd,OUT PRO_FRAME_BODY_LIST & lResult)
{
	RcdErrLogWithParentClass("ConvertCommonMsgToPro()方法在规约客户端有效,服务端暂不支持","TNXEcProAsdu17");

	return EC_PRO_CVT_NOSUPPORT;
}

/**
* @brief		转换本地所有历史信息到规约信息体			
* @param[in]    ASDU_ADDR AsduAddr
* @param[in]    ASDU_TIME AsduTime
* @param[out]   PRO_FRAME_BODY_LIST & lResult:规约信息体.
* @param[out]   char * cError:错误信息
* @return		0-执行成功；其他-执行失败
* @note 
**/
int TNXEcProAsdu17FJ::_CvtAllHisToPro(OUT MAP_ASDU_HIS_INFO_LIST & mapAsduHistInfoList)
{
	_CvtHisEventToPro(mapAsduHistInfoList);
	_CvtHisStrapChangeToPro(mapAsduHistInfoList);
	_CvtHisAlarmToPro(mapAsduHistInfoList);
	return 0;
}

/**
* @brief		将AsduHisInfo结构体,转成PRO_FRAME_DATA			
* @param[in]    ASDU_HIS_INFO AsduInfo
* @param[out]   PRO_FRAME_DATA FramData
* @return		0-执行成功；其他-执行失败
**/
int TNXEcProAsdu17FJ::__FormatAsduHisInfoToFramedata(IN ASDU_HIS_INFO * pHisInfo,OUT PRO_FRAME_DATA & HisData)
{
	CTimeConvert CCTime;
	string sFaultTm;
	PRO_FRAME_DATA vDat;
	short nValue;
	float fValue;

	HisData.push_back(pHisInfo->eType);				//事件类型 1
	HisData.push_back((u_int8)pHisInfo->InfoObj.nFun);		//fun 1
	HisData.push_back((u_int8)pHisInfo->InfoObj.nInf);		//inf 1
	HisData.push_back((u_int8)pHisInfo->InfoObj.nDpi);		//Dpi 1
	
	vDat.clear();
	vDat.resize(2);
	nValue = pHisInfo->InfoTime.nRetTime;
	_REVERSE_BYTE_ORDER_16(nValue);
	memcpy(&vDat[0],&nValue,2); //ret 2
	HisData.insert(HisData.end(),vDat.begin(),vDat.end());//相对时间

	vDat.clear();
	vDat.resize(2);
	nValue = pHisInfo->FaultInfo.nFan;
	_REVERSE_BYTE_ORDER_16(nValue);
	memcpy(&vDat[0],&nValue,2); //ret 2
	HisData.insert(HisData.end(),vDat.begin(),vDat.end());//故障序号

	CCTime.SetTime(pHisInfo->InfoTime.nInfoHappenUtc,pHisInfo->InfoTime.nInfoHappenMs);
	CCTime.GetCP56TIMe(sFaultTm);
	HisData.insert(HisData.end(),sFaultTm.begin(),sFaultTm.end());  //故障时间 7

	CCTime.SetTime(pHisInfo->InfoTime.nInfoRcvUtc,pHisInfo->InfoTime.nInfoRcvMs);
	CCTime.GetCP56TIMe(sFaultTm);
	HisData.insert(HisData.end(),sFaultTm.begin(),sFaultTm.end());  //子站接收时间 7

	HisData.push_back(pHisInfo->GenList.size());
	if( pHisInfo->GenList.size() == 0 )
	{		
		return 0;
	}

	ASDU_GEN_INFO_LIST::iterator iteGen=pHisInfo->GenList.begin();
	while(iteGen != pHisInfo->GenList.end())
	{
		HisData.push_back((u_int8)(iteGen->nGroup));
		HisData.push_back((u_int8)(iteGen->nEntry));
		HisData.push_back((u_int8)(iteGen->nKod));
		HisData.push_back((u_int8)(iteGen->nDataType));
		HisData.push_back((u_int8)(iteGen->nDataSize));
		HisData.push_back((u_int8)(iteGen->nDataNumber));
		HisData.insert(HisData.end(),iteGen->vGid.begin(),iteGen->vGid.end());

		iteGen++;
	}
	vDat.clear();
	return 0;
}

/**
* @brief		查询出符合条件的历史动作信息,并转换到对应结构体列表中.			
* @param[in]    ASDU_ADDR AsduAddr-地址信息
* @param[in]    ASDU_TIME AsduTime-时间范围
* @param[out]   ASDU_HIS_INFO_LIST lAsduHisInfo :数据结构体列表.
* @return		0-执行成功；其他-执行失败
**/
int TNXEcProAsdu17FJ::_CvtHisEventToPro(OUT MAP_ASDU_HIS_INFO_LIST & mapAsduHistInfoList)
{
	CPlmRecordSet RcdSet_Event;
	CPlmRecordSet RcdSet_FaultTag;
	CPlmRecordSet RcdSet_StartTag;
		
	UINT nSetNum;
	ASDU_HIS_INFO HisInfo_Event;
	CTimeConvert CCTime;

	int nEventId;
	char cError[255] = "";
	string sValue,sValue1;
	int    nValue;	
	
	//1-获取设备的历史动作信息
	if (__GetHisEventDataSet(0,RcdSet_Event) < 0) //
	{
		_ZERO_MEM(cError,255);
		sprintf(cError,"__GetHisEventDataSet():在获取装置地址为%d的历史动作信息时异常。",m_nIedId);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu17FJ");
		return EC_PRO_CVT_FAIL;
	}


	//2-获取设备的故障特征量历史数据（指定时间范围）
	if (__GetHisEventDataSet(1,RcdSet_FaultTag) < 0)
	{
		_ZERO_MEM(cError,255);
		sprintf(cError,"__GetHisEventDataSet():在获取装置地址为%d的历史故障量信息时异常。",m_nIedId);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu17FJ");
		return EC_PRO_CVT_FAIL;
	}

	// //3-获取设备的启动特征量历史数据（指定时间范围）
	// if (__GetHisEventDataSet(2,RcdSet_StartTag) < 0)
	// {
	// 	_ZERO_MEM(cError,255);
	// 	sprintf(cError,"__GetHisEventDataSet():在获取装置地址为%d的历史故障量信息时异常。",m_nIedId);
	// 	RcdErrLogWithParentClass(cError,"TNXEcProAsdu17FJ");
	// 	return EC_PRO_CVT_FAIL;
	// }

	//将获取到的历史动作数据表中的历史故障特征量数据,做成以故障序号为key的map表.以便和动作的故障序号对比。
	FAULTTAB_INFO_MAP mapFaultTag;
	FAULTTAB_INFO_MAP::iterator iteFaultTag;
	ASDU_GEN_INFO_CPU_LIST::iterator iteGenInfoListCpu;
	ASDU_GEN_INFO_CPU_LIST::iterator iteDel;
	__FormatFaultTagDataSetToFanMapSet(m_AsduAddr,RcdSet_FaultTag,mapFaultTag);

	RcdSet_Event.get_record_num(nSetNum);//|1-data_code|2-curvalue|3-curvaluetm|4-curms|5-relativems|6-subgridfan|7-ld_code|8-recvtm
	//RcdSet.move_to_top();
	sprintf(cError,"_CvtHisEventToPro():从数据库中查找到%d个历史动作事件",nSetNum);
	RcdTrcLogWithParentClass(cError,"TNXEcProAsdu17FJ");

	const EVENT_TB * pEventTb = NULL;
	for (int i=1;i < nSetNum+1; i++)
	{
		//先获取历史动作信息中的cpu
		RcdSet_Event.get_field_value(7,sValue);
		nValue=atoi(sValue.c_str());
		HisInfo_Event.Addr.nCpu =nValue;				//装置cpu

		RcdSet_Event.get_field_value(1,sValue);
		nEventId=atoi(sValue.c_str());
		pEventTb = m_pModelSeek->GetIedEventCfg(m_nIedId,HisInfo_Event.Addr.nCpu,nEventId);
		if (NULL == pEventTb)
		{
			_ZERO_MEM(cError,255);
			sprintf(cError,"__QueryHisEventToAsduInfo():在获取装置地址为%d的动作配置信息时异常。",m_nIedId);
			RcdErrLogWithParentClass(cError,"TNXEcProAsdu17FJ");
			return EC_PRO_CVT_FAIL;
		}

		HisInfo_Event.Addr.nAddr=m_AsduAddr.nAddr;				//装置地址		

		HisInfo_Event.eType	  =EVENT_ACTION;				//事件类型:故障动作信息
		HisInfo_Event.FaultInfo.fScl=0;						//短路位置:全部设置为0.
		RcdSet_Event.get_field_value(6,sValue);
		HisInfo_Event.FaultInfo.nFan=atoi(sValue.c_str());	//故障序号
		HisInfo_Event.FaultInfo.nFpt=0;						//故障相别

		RcdSet_Event.get_field_value(3,sValue);
		RcdSet_Event.get_field_value(4,sValue1);
		nValue=atoi(sValue1.c_str());//毫秒
		CCTime.SetTime(sValue,nValue);
		HisInfo_Event.InfoTime.nInfoHappenUtc=CCTime.GetTimeOfTIME_T(); //故障时间
		HisInfo_Event.InfoTime.nInfoHappenMs =nValue;
		//子站接收时间
		RcdSet_Event.get_field_value(8,sValue);
		CCTime.SetTime(sValue);
		HisInfo_Event.InfoTime.nInfoRcvUtc = CCTime.GetTimeOfTIME_T();//子站接收时间
		HisInfo_Event.InfoTime.nInfoRcvMs  = 0;

		RcdSet_Event.get_field_value(5,sValue);
		HisInfo_Event.InfoTime.nRetTime = atoi(sValue.c_str());//相对时间

		HisInfo_Event.InfoObj.nFun = pEventTb->n_outfun103;	//fun
		HisInfo_Event.InfoObj.nInf = pEventTb->n_outinf103;	//inf
		RcdSet_Event.get_field_value(2,sValue);
		HisInfo_Event.InfoObj.nDpi = atoi(sValue.c_str())+1;	//dpi
		
		//关联动作事件和特征量列表，以动作事件为主.
		iteFaultTag = mapFaultTag.find(HisInfo_Event.FaultInfo.nFan);
		if (iteFaultTag != mapFaultTag.end())  //故障序号匹配后，要在看CPU是否也匹配。
		{
			iteGenInfoListCpu = (iteFaultTag->second)->begin();
			while (iteGenInfoListCpu != (iteFaultTag->second)->end() )  
			{
				if (iteGenInfoListCpu->nCpu == HisInfo_Event.Addr.nCpu) //CPU也匹配，将这个特征量数据关联到本次动作事件。关联后，删除这个特征量数据，防止一个特征量与多个动作关联。
				{
					HisInfo_Event.GenList.push_back(iteGenInfoListCpu->AsduGenInfo);
					iteGenInfoListCpu->AsduGenInfo.vGid.clear();
					iteFaultTag->second->erase(iteGenInfoListCpu++);
					continue;
				}
				else{
					iteGenInfoListCpu++;
				}						
			}
		}

		//将建立好关联的动作信息，做成以cpu为key的map表。
		if (mapAsduHistInfoList.count(HisInfo_Event.Addr.nCpu) <= 0) //还没有该键值和对应的列表
		{
			ASDU_HIS_INFO_LIST* plAsduHisInfo = new ASDU_HIS_INFO_LIST;
			if (NULL == plAsduHisInfo)
			{
				RcdSet_Event.clear_result();
				RcdSet_FaultTag.clear_result();
				RcdErrLogWithParentClass("_CvtHisEventToPro(): 创建ASDU_HIS_INFO_LIST时异常。","TNXEcProAsdu17FJ");
				return EC_PRO_CVT_FAIL;
			}
			mapAsduHistInfoList[HisInfo_Event.Addr.nCpu] = plAsduHisInfo;
		}
		mapAsduHistInfoList[HisInfo_Event.Addr.nCpu]->push_back(HisInfo_Event);
		__clear_GenList(HisInfo_Event.GenList);
		HisInfo_Event.GenList.clear();

		RcdSet_Event.move_to_next();		
	}
	//清理mapFaultTag。iteGenInfoListCpu
	__clear_MapFaultTag(mapFaultTag);

	//清理查询数据集。
	RcdSet_Event.clear_result();
	RcdSet_FaultTag.clear_result();

	return 0;
}

/**
* @brief		转换本地历史告警信息到规约信息体			
* @param[in]    ASDU_ADDR AsduAddr
* @param[in]    ASDU_TIME AsduTime
* @param[out]   ASDU_HIS_INFO_LIST & lAsduHisInfo:结果队列.
* @return		0-执行成功；其他-执行失败
**/
int TNXEcProAsdu17FJ::_CvtHisAlarmToPro(OUT MAP_ASDU_HIS_INFO_LIST & mapAsduHistInfoList)
{
	char cError[255] = "";
	CPlmRecordSet RcdSet;
	UINT nSetNum;
	ASDU_HIS_INFO HisInfo;
	CTimeConvert CCTime;

	int nAlarmId;
	string sValue,sValue1;
	int    nValue;
	const ALARM_TB * pAlarmTb = NULL;

	if (__GetHisAlarmDataSet(RcdSet) < 0) return -1;

	RcdSet.get_record_num(nSetNum);//|1-data_code|2-curvalue|3-curvaluetm|4-curms|5-ld_code
	for (int i=0;i < nSetNum; i++)
	{
		RcdSet.get_field_value(1,sValue);
		nAlarmId=atoi(sValue.c_str());

		HisInfo.Addr.nAddr=m_AsduAddr.nAddr;				//装置地址
		RcdSet.get_field_value(5,sValue);
		nValue=atoi(sValue.c_str());
		HisInfo.Addr.nCpu =nValue;				//装置cpu
		HisInfo.eType	  =EVENT_ALARM;					//事件类型:故障测距.
		HisInfo.FaultInfo.fScl=0;						//短路位置:全部设置为0.
		HisInfo.FaultInfo.nFan=0;						//故障序号
		HisInfo.FaultInfo.nFpt=0;						//故障相别

		RcdSet.get_field_value(3,sValue);
		RcdSet.get_field_value(4,sValue1);
		nValue=atoi(sValue1.c_str());//毫秒
		CCTime.SetTime(sValue,nValue);
		HisInfo.InfoTime.nInfoHappenUtc=CCTime.GetTimeOfTIME_T(); //故障时间
		HisInfo.InfoTime.nInfoHappenMs =nValue;
		HisInfo.InfoTime.nRetTime = 0;//相对时间

		//子站接收时间
		RcdSet.get_field_value(6,sValue);
		CCTime.SetTime(sValue);
		HisInfo.InfoTime.nInfoRcvUtc = CCTime.GetTimeOfTIME_T();//子站接收时间
		HisInfo.InfoTime.nInfoRcvMs  = 0;

		pAlarmTb = m_pModelSeek->GetIedAlarmCfg(m_nIedId,HisInfo.Addr.nCpu,nAlarmId);
		if (pAlarmTb == NULL)
		{
			sprintf(cError,"_CvtHisAlarmToPro:获取设备(IEDID=%d,CPU=%d,ALARMID=%d)的告警配置信息出错.",m_nIedId,HisInfo.Addr.nCpu,nAlarmId);
			RcdErrLogWithParentClass(cError,"TNXEcProAsdu17");
			RcdSet.move_to_next();
			continue;
		}
		HisInfo.InfoObj.nFun = pAlarmTb->n_outfun103;	//fun
		HisInfo.InfoObj.nInf = pAlarmTb->n_outinf103;	//inf	
		RcdSet.get_field_value(2,sValue);
		HisInfo.InfoObj.nDpi = atoi(sValue.c_str())+1;			//dpi

		if (mapAsduHistInfoList.count(HisInfo.Addr.nCpu) <= 0) //还没有该键值和对应的列表
		{
			ASDU_HIS_INFO_LIST* plAsduHisInfo = new ASDU_HIS_INFO_LIST;
			if (NULL == plAsduHisInfo)
			{
				RcdSet.clear_result();
				RcdErrLogWithParentClass("_CvtHisAlarmToPro(): 创建ASDU_HIS_INFO_LIST时异常。","TNXEcProAsdu17");
				return EC_PRO_CVT_FAIL;
			}
			mapAsduHistInfoList[HisInfo.Addr.nCpu] = plAsduHisInfo;
		}
		mapAsduHistInfoList[HisInfo.Addr.nCpu]->push_back(HisInfo);

		RcdSet.move_to_next();		
	}
	RcdSet.clear_result();
	return 0;
}
/**
* @brief		根据故障特征量数据集,做成以故障序号为key的MAP表.
* @param[in]    ASDU_ADDR AsduAddr:设备地址信息
* @param[in]    CPlmRecordSet & RcdSet_FaultTag-故障特征量数据集
* @param[out]    FAULTTAB_INFO_MAP & mapFaultTag
**/
void TNXEcProAsdu17FJ::__FormatFaultTagDataSetToFanMapSet(IN ASDU_ADDR AsduAddr,IN CPlmRecordSet & RcdSet_FaultTag,OUT FAULTTAB_INFO_MAP & mapFaultTag)
{
	char cError[255] = "";
	UINT nRetNum;
	RcdSet_FaultTag.get_record_num(nRetNum);
	if (nRetNum == 0) return;

	const IED_TB* pIedTb = m_pModelSeek->GetIedBasicCfgByAddr103(AsduAddr.nAddr);
	if ( NULL == pIedTb)
	{
		sprintf(cError,"__FormatFaultTagDataSetToFanMapSet():在获取103地址为%d的设备的配置信息时异常。",AsduAddr.nAddr);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu17FJ");
		return ;
	}
	int nIedId=pIedTb->n_obj_id;

	ASDU_GEN_INFO_CPU GenInfo_Cpu;
	//ASDU_GEN_INFO GenInfo;
	int nFaultTabCode,nFan;
	string sValue;
	float fValue;
	int   nValue,nCpu;
	char  cValue[128]="";
	const FAULTTAG_TB * pFaultTb = NULL;

	for(int i=1; i< nRetNum+1;i++)//|1-data_code|2-curvalue|3-curvaluetm|4-curms|5-relativems|6-subgridfan|7-ld_code
	{
		//获取cpu号
		RcdSet_FaultTag.get_field_value(7,sValue);
		GenInfo_Cpu.nCpu = atoi(sValue.c_str());

		RcdSet_FaultTag.get_field_value(1,sValue);
		nFaultTabCode = atoi(sValue.c_str());//故障特征量编号
		pFaultTb = m_pModelSeek->GetIedFaultCfg(nIedId,GenInfo_Cpu.nCpu,nFaultTabCode);
		if ( NULL == pFaultTb)
		{
			sprintf(cError,"__FormatFaultTagDataSetToFanMapSet():在获取设备编号为%d的设备的故障量编号%d的配置信息时异常。",nIedId,nFaultTabCode);
			RcdErrLogWithParentClass(cError,"TNXEcProAsdu17FJ");
			i++;
			continue;
		}

		RcdSet_FaultTag.get_field_value(6,sValue);
		nFan = atoi(sValue.c_str());//故障序号

		RcdSet_FaultTag.get_field_value(2,sValue);		
		switch(pFaultTb->e_psrdatatype)
		{
		case VALUE_FLOAT:
			fValue = atof(sValue.c_str());
			__SetGenericFloatData(pFaultTb->n_outgroup103,pFaultTb->n_outitem103,1,fValue,GenInfo_Cpu.AsduGenInfo);
			break;
		case VALUE_HEX:
		case VALUE_BIN:
		case VALUE_INT:
			nValue = atoi(sValue.c_str());
			__SetGenericIntData(pFaultTb->n_outgroup103,pFaultTb->n_outitem103,1,nValue,GenInfo_Cpu.AsduGenInfo);
			break;
		case VALUE_CHAR:
		default:
			memcpy(cValue,sValue.c_str(),sValue.length());
			__SetGenericStrData(pFaultTb->n_outgroup103,pFaultTb->n_outitem103,1,cValue,GenInfo_Cpu.AsduGenInfo);
			break;
		}

		if (0 == nFan)//如果故障序号是0，就不上送
		{
			RcdSet_FaultTag.move_to_next();	
			continue;
		}

		//做成故障序号,特征量信息列表的MAP表.
		if ( mapFaultTag.count(nFan) <= 0)
		{
			ASDU_GEN_INFO_CPU_LIST* pAsduGenInfoCpuList = new ASDU_GEN_INFO_CPU_LIST;
			if ( pAsduGenInfoCpuList == NULL)
			{
				RcdErrLogWithParentClass("_CvtHisEventToPro():创建ASDU_GEN_INFO_CPU_LIST失败。","TNXEcProAsdu17FJ");
				return ;
			}

			mapFaultTag[nFan] = pAsduGenInfoCpuList;
			pAsduGenInfoCpuList = NULL;
		}
		
		mapFaultTag[nFan]->push_back(GenInfo_Cpu);
		GenInfo_Cpu.AsduGenInfo.vGid.clear();
		RcdSet_FaultTag.move_to_next();	
	}

}

/**
* @brief		清理故障量数据MAP表。			
* @param[in]    FAULTTAB_INFO_MAP & mapFaultTag
**/
void TNXEcProAsdu17FJ::__clear_MapFaultTag(FAULTTAB_INFO_MAP & mapFaultTag)
{
	FAULTTAB_INFO_MAP::iterator iteFaultTag = mapFaultTag.begin();
	ASDU_GEN_INFO_CPU_LIST::iterator iteGenInfoListCpu;
	while(iteFaultTag != mapFaultTag.end())
	{
		iteGenInfoListCpu = (iteFaultTag->second)->begin();
		while(iteGenInfoListCpu != (iteFaultTag->second)->end())
		{
			(iteGenInfoListCpu->AsduGenInfo).vGid.clear();
			iteGenInfoListCpu++;
		}
		(iteFaultTag->second)->clear();
		iteFaultTag++;
	}
}

/**
* @brief		清理ASDU_GEN_INFO中的list。			
* @param[in]    ASDU_GEN_INFO_LIST & GenInfoList
**/
void TNXEcProAsdu17FJ::__clear_GenList(ASDU_GEN_INFO_LIST & GenInfoList)
{
	ASDU_GEN_INFO_LIST::iterator iteGen = GenInfoList.begin();
	while(iteGen != GenInfoList.end())
	{
		iteGen->vGid.clear();
		iteGen++;
	}
}

/**
* @brief		转换本地历史硬压板变位信息到规约信息体			
* @param[out]   MAP_ASDU_HIS_INFO_LIST & mapAsduHistInfoList:历史信息列表
* @return		0-执行成功；其他-执行失败
**/
int TNXEcProAsdu17FJ::_CvtHisStrapChangeToPro(OUT MAP_ASDU_HIS_INFO_LIST & mapAsduHistInfoList)
{
	// 1. 调用基类方法，获取硬压板变位信息
	TNXEcProAsdu17::_CvtHisStrapChangeToPro(mapAsduHistInfoList);
	
	// 2. 新增：查询软压板历史变位信息
	_CvtHisSoftStrapToPro(mapAsduHistInfoList);
	
	// 3. 新增：查询通信状态历史信息
	_CvtHisCommuStatusToPro(mapAsduHistInfoList);
	
	return 0;
}

/**
* @brief		查询软压板历史变位信息，转换为协议信息
* @param[out]   MAP_ASDU_HIS_INFO_LIST & mapAsduHistInfoList:历史信息列表
* @return       0-执行成功，其他-执行失败
**/
int TNXEcProAsdu17FJ::_CvtHisSoftStrapToPro(OUT MAP_ASDU_HIS_INFO_LIST & mapAsduHistInfoList)
{
	char cError[255] = "";
	CPlmRecordSet RcdSet;
	UINT nSetNum;
	ASDU_HIS_INFO HisInfo;
	CTimeConvert CCTime;

	int nStrapId;
	string sValue,sValue1;
	int    nValue;
	const STRAP_TB * pStrapTb = NULL;

	// 软压板
	if (__GetHisSoftStrapDataSet(RcdSet) < 0) return -1;

	RcdSet.get_record_num(nSetNum);//|1-strap_code|2-curvalue|3-curvaluetm|4-curms|5-ld_code
	for (int i=0;i < nSetNum; i++)
	{
		HisInfo.Addr.nAddr=m_AsduAddr.nAddr;				//装置地址
		RcdSet.get_field_value(5,sValue);
		nValue=atoi(sValue.c_str());
		HisInfo.Addr.nCpu =nValue;						//装置cpu

		RcdSet.get_field_value(1,sValue);
		nStrapId=atoi(sValue.c_str());
		pStrapTb = m_pModelSeek->GetIedSoftStrapCfg(m_nIedId,HisInfo.Addr.nCpu,nStrapId);
		if (pStrapTb == NULL)
		{
			sprintf(cError,"_CvtHisSoftStrapToPro:获取设备(IEDID=%d,CPU=%d,SOFTSTRAPID=%d)软压板信息出错!",m_nIedId,HisInfo.Addr.nCpu,nStrapId);
			RcdErrLogWithParentClass(cError,"TNXEcProAsdu17FJ");
			RcdSet.move_to_next();
			continue;
		}
		
		HisInfo.eType	  =EVENT_SOFTSTRAP;				//事件类型:软压板变位
		HisInfo.FaultInfo.fScl=0;						//短路位置:全部设置为0.
		HisInfo.FaultInfo.nFan=0;						//故障序号
		HisInfo.FaultInfo.nFpt=0;						//故障相别

		RcdSet.get_field_value(3,sValue);
		RcdSet.get_field_value(4,sValue1);
		nValue=atoi(sValue1.c_str());//毫秒
		CCTime.SetTime(sValue,nValue);
		HisInfo.InfoTime.nInfoHappenUtc=CCTime.GetTimeOfTIME_T(); //变位时间
		HisInfo.InfoTime.nInfoHappenMs =nValue;
		HisInfo.InfoTime.nInfoRcvUtc = HisInfo.InfoTime.nInfoHappenUtc;
		HisInfo.InfoTime.nInfoRcvMs  = HisInfo.InfoTime.nInfoHappenMs;

		HisInfo.InfoTime.nRetTime = 0;					//相对时间

		HisInfo.InfoObj.nFun = pStrapTb->n_outfun103;	//fun
		HisInfo.InfoObj.nInf = pStrapTb->n_outinf103;	//inf
		RcdSet.get_field_value(2,sValue);
		HisInfo.InfoObj.nDpi = atoi(sValue.c_str())+1;	//dpi

		if (mapAsduHistInfoList.count(HisInfo.Addr.nCpu) <= 0) //还没有该键值和对应的列表
		{
			ASDU_HIS_INFO_LIST* plAsduHisInfo = new ASDU_HIS_INFO_LIST;
			if (NULL == plAsduHisInfo)
			{
				RcdSet.clear_result();
				RcdErrLogWithParentClass("_CvtHisSoftStrapToPro(): 创建ASDU_HIS_INFO_LIST时异常。","TNXEcProAsdu17FJ");
				return EC_PRO_CVT_FAIL;
			}
			mapAsduHistInfoList[HisInfo.Addr.nCpu] = plAsduHisInfo;
		}
		mapAsduHistInfoList[HisInfo.Addr.nCpu]->push_back(HisInfo);

		RcdSet.move_to_next();		
	}
	RcdSet.clear_result();
	return 0;
}

/**
* @brief		查询通信状态历史信息，转换为协议信息
* @param[out]   MAP_ASDU_HIS_INFO_LIST & mapAsduHistInfoList:历史信息列表  
* @return       0-执行成功，其他-执行失败
**/
int TNXEcProAsdu17FJ::_CvtHisCommuStatusToPro(OUT MAP_ASDU_HIS_INFO_LIST & mapAsduHistInfoList)
{
	char cError[255] = "";
	CPlmRecordSet RcdSet;
	UINT nSetNum;
	ASDU_HIS_INFO HisInfo;
	CTimeConvert CCTime;

	int nCommuStatus;
	string sValue,sValue1;
	int    nValue;

	// 通信状态
	if (__GetHisCommuStatusDataSet(RcdSet) < 0) return -1;

	RcdSet.get_record_num(nSetNum);//|1-cmmustat|2-curvaluetm|3-curms|4-chgreason_obj
	for (int i=0;i < nSetNum; i++)
	{
		HisInfo.Addr.nAddr=m_AsduAddr.nAddr;				//装置地址
		HisInfo.Addr.nCpu =0;							//通信状态不分CPU

		RcdSet.get_field_value(1,sValue);
		nCommuStatus=atoi(sValue.c_str());
		
		HisInfo.eType	  =EVENT_SOFTSTRAP;				//事件类型:复用软压板变位
		HisInfo.FaultInfo.fScl=0;						//短路位置:全部设置为0.
		HisInfo.FaultInfo.nFan=0;						//故障序号
		HisInfo.FaultInfo.nFpt=0;						//故障相别

		RcdSet.get_field_value(2,sValue);
		RcdSet.get_field_value(3,sValue1);
		nValue=atoi(sValue1.c_str());//毫秒
		CCTime.SetTime(sValue,nValue);
		HisInfo.InfoTime.nInfoHappenUtc=CCTime.GetTimeOfTIME_T(); //状态变化时间
		HisInfo.InfoTime.nInfoHappenMs =nValue;
		HisInfo.InfoTime.nInfoRcvUtc = HisInfo.InfoTime.nInfoHappenUtc;
		HisInfo.InfoTime.nInfoRcvMs  = HisInfo.InfoTime.nInfoHappenMs;

		HisInfo.InfoTime.nRetTime = 0;					//相对时间

		HisInfo.InfoObj.nFun = 0;						//根据配置确定
		HisInfo.InfoObj.nInf = 0;						//根据配置确定
		HisInfo.InfoObj.nDpi = nCommuStatus + 1;			//通信状态值

		if (mapAsduHistInfoList.count(HisInfo.Addr.nCpu) <= 0) //还没有该键值和对应的列表
		{
			ASDU_HIS_INFO_LIST* plAsduHisInfo = new ASDU_HIS_INFO_LIST;
			if (NULL == plAsduHisInfo)
			{
				RcdSet.clear_result();
				RcdErrLogWithParentClass("_CvtHisCommuStatusToPro(): 创建ASDU_HIS_INFO_LIST时异常。","TNXEcProAsdu17FJ");
				return EC_PRO_CVT_FAIL;
			}
			mapAsduHistInfoList[HisInfo.Addr.nCpu] = plAsduHisInfo;
		}
		mapAsduHistInfoList[HisInfo.Addr.nCpu]->push_back(HisInfo);

		RcdSet.move_to_next();		
	}
	RcdSet.clear_result();
	return 0;
}

/**
* @brief		从数据库获取历史软压板数据
* @param[out]   CPlmRecordSet & RcdSet:数据集
* @return       0-执行成功，其他-执行失败
**/
int TNXEcProAsdu17FJ::__GetHisSoftStrapDataSet(OUT CPlmRecordSet & RcdSet)
{
	char cError[255]="";
	char cDbError[255]="";
	//查库;
	INXEcModelMgr * pModelMgr;
	DB_OPER_PARAM suParam;				 // 数据库查询参数结构体
	DB_FIELD_DATA suField;               //字段结构体
	DB_CONDITION  suCondition;           //条件结构体

	//要查询出的字段
	suField.str_fd_name="strap_code";		//软压板编号  
	suParam.lst_fddata.push_back(suField);
	suField.str_fd_name="curvalue";			//当前值
	suParam.lst_fddata.push_back(suField);
	suField.str_fd_name="curvaluetm";		//变位时间
	suParam.lst_fddata.push_back(suField);
	suField.str_fd_name="curvaluems";		//变位时间毫秒
	suParam.lst_fddata.push_back(suField);
	suField.str_fd_name="ld_code";			//CPU编号
	suParam.lst_fddata.push_back(suField);

	//查询表名
	suParam.lst_tablename.push_back("nx_t_ied_softstrap_data");
	
	// 添加调试日志：查询表名
	char cDebugLog[255];
	sprintf(cDebugLog,"[__GetHisSoftStrapDataSet] 设备%d开始查询表: nx_t_ied_softstrap_data", m_nIedId);
	RcdTrcLogWithParentClass(cDebugLog,"TNXEcProAsdu17FJ");

	//查询条件1  设备ID
	suCondition.str_cdt_name="ied_obj";
	char cTemp[50]="";
	sprintf(cTemp,"%d",m_nIedId);
	suCondition.str_cdt_value=cTemp;
	suCondition.e_cdt_type		=CDT_TYPE_EQUAL;
	suCondition.e_cdt_val_type  =FD_TYPE_NUMERIC;
	suCondition.e_cdt_logical   =CDT_LOGIAL_AND;
	suParam.lst_condition.push_back(suCondition);

	//条件2-1 时间范围
	CTimeConvert CCTime(m_AsduTime.nInfoHappenUtc,m_AsduTime.nInfoHappenMs);
	string sBeginTm,sEndTm;
	CCTime.GetStandStringTime(sBeginTm);
	suCondition.str_cdt_name="curvaluetm";
	suCondition.str_cdt_value=sBeginTm;
	suCondition.e_cdt_type		=CDT_TYPE_GREATER_EQUAL;
	suCondition.e_cdt_val_type  =FD_TYPE_CHAR;
	suCondition.e_cdt_logical   =CDT_LOGIAL_AND;
	suParam.lst_condition.push_back(suCondition);
	
	// 添加调试日志：开始时间
	sprintf(cDebugLog,"[__GetHisSoftStrapDataSet] 设备%d查询条件 - 开始时间: %s", m_nIedId, sBeginTm.c_str());
	RcdTrcLogWithParentClass(cDebugLog,"TNXEcProAsdu17FJ");
	
	//条件2-2 时间范围
	CCTime.SetTime(m_AsduTime.nInfoRcvUtc,m_AsduTime.nInfoRcvMs);
	CCTime.GetStandStringTime(sEndTm);
	suCondition.str_cdt_name="curvaluetm";
	suCondition.str_cdt_value=sEndTm;
	suCondition.e_cdt_type		=CDT_TYPE_SMALLER_EQUAL;
	suCondition.e_cdt_val_type  =FD_TYPE_CHAR;
	suCondition.e_cdt_logical   =CDT_LOGIAL_AND;
	suParam.lst_condition.push_back(suCondition);
	
	// 添加调试日志：结束时间
	sprintf(cDebugLog,"[__GetHisSoftStrapDataSet] 设备%d查询条件 - 结束时间: %s", m_nIedId, sEndTm.c_str());
	RcdTrcLogWithParentClass(cDebugLog,"TNXEcProAsdu17FJ");

	//条件3 变位属性-变位上送,还是系统对比生成.对比生成的不送.  
	suCondition.str_cdt_name="savereason";
	suCondition.str_cdt_value="0";
	suCondition.e_cdt_type		=CDT_TYPE_EQUAL;
	suCondition.e_cdt_val_type  =FD_TYPE_NUMERIC;
	suParam.lst_condition.push_back(suCondition);
	
	// 添加调试日志：查询条件
	sprintf(cDebugLog,"[__GetHisSoftStrapDataSet] 设备%d查询条件 - 设备ID: %d, savereason: 0", m_nIedId, m_nIedId);
	RcdTrcLogWithParentClass(cDebugLog,"TNXEcProAsdu17FJ");

	//查询
	pModelMgr=m_pModelSeek->GetModelMgrObj();	
	if (NULL == pModelMgr)
	{
		RcdErrLogWithParentClass("[__GetHisSoftStrapDataSet] 模型管理对象指针失败.","TNXEcProAsdu17FJ");
		return EC_PRO_CVT_FAIL;
	}
	int nRet=pModelMgr->dbm_select_records(&suParam,RcdSet,false,cDbError);
	if(nRet!=0)
	{
		_clear_db_opera_param(suParam);  // 清除查询信息列表
		sprintf(cError,"查询设备%d的软压板历史变位数据时出错[%s]",m_nIedId,cDbError);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu17FJ");
		return -1;
	}
	_clear_db_opera_param(suParam);  // 清除查询信息列表
	
	// 添加成功日志
	UINT nRecordNum = 0;
	RcdSet.get_record_num(nRecordNum);
	char cSuccessLog[255];
	sprintf(cSuccessLog,"[__GetHisSoftStrapDataSet] 成功查询设备%d的软压板历史变位数据，记录数量=%d", m_nIedId, nRecordNum);
	RcdTrcLogWithParentClass(cSuccessLog,"TNXEcProAsdu17FJ");
	
	// 添加查询结果详情日志
	if (nRecordNum > 0)
	{
		RcdSet.move_to_top();
		string sValue;
		for (int i = 0; i < nRecordNum && i < 5; i++) // 只显示前5条记录
		{
			RcdSet.get_field_value(1, sValue); // strap_code
			string sStrapCode = sValue;
			RcdSet.get_field_value(2, sValue); // curvalue
			string sCurValue = sValue;
			RcdSet.get_field_value(3, sValue); // curvaluetm
			string sCurValueTm = sValue;
			
			sprintf(cSuccessLog,"[__GetHisSoftStrapDataSet] 记录%d: 软压板=%s, 值=%s, 时间=%s", 
				i+1, sStrapCode.c_str(), sCurValue.c_str(), sCurValueTm.c_str());
			RcdTrcLogWithParentClass(cSuccessLog,"TNXEcProAsdu17FJ");
			
			RcdSet.move_to_next();
		}
		if (nRecordNum > 5)
		{
			sprintf(cSuccessLog,"[__GetHisSoftStrapDataSet] ... 还有%d条记录未显示", nRecordNum - 5);
			RcdTrcLogWithParentClass(cSuccessLog,"TNXEcProAsdu17FJ");
		}
	}
	
	return 0;
}

/**
* @brief		从数据库获取历史通信状态数据
* @param[out]   CPlmRecordSet & RcdSet:数据集
* @return       0-执行成功，其他-执行失败  
**/
int TNXEcProAsdu17FJ::__GetHisCommuStatusDataSet(OUT CPlmRecordSet & RcdSet)
{
	char cError[255]="";
	char cDbError[255]="";
	//查库;
	INXEcModelMgr * pModelMgr;
	DB_OPER_PARAM suParam;				 // 数据库查询参数结构体
	DB_FIELD_DATA suField;               //字段结构体
	DB_CONDITION  suCondition;           //条件结构体

	//要查询出的字段
	suField.str_fd_name="cmmustat";			//通信状态
	suParam.lst_fddata.push_back(suField);
	suField.str_fd_name="curvaluetm";		//状态变化时间
	suParam.lst_fddata.push_back(suField);
	suField.str_fd_name="chgreason_obj";		//变化原因
	suParam.lst_fddata.push_back(suField);

	//查询表名
	suParam.lst_tablename.push_back("nx_t_ied_commu_status");
	
	// 添加调试日志：查询表名
	char cDebugLog[255];
	sprintf(cDebugLog,"[__GetHisCommuStatusDataSet] 设备%d开始查询表: nx_t_ied_commu_status", m_nIedId);
	RcdTrcLogWithParentClass(cDebugLog,"TNXEcProAsdu17FJ");

	//查询条件1  设备ID
	suCondition.str_cdt_name="ied_obj";
	char cTemp[50]="";
	sprintf(cTemp,"%d",m_nIedId);
	suCondition.str_cdt_value=cTemp;
	suCondition.e_cdt_type		=CDT_TYPE_EQUAL;
	suCondition.e_cdt_val_type  =FD_TYPE_NUMERIC;
	suCondition.e_cdt_logical   =CDT_LOGIAL_AND;
	suParam.lst_condition.push_back(suCondition);

	//条件2-1 时间范围
	CTimeConvert CCTime(m_AsduTime.nInfoHappenUtc,m_AsduTime.nInfoHappenMs);
	string sBeginTm,sEndTm;
	CCTime.GetStandStringTime(sBeginTm);
	suCondition.str_cdt_name="curvaluetm";
	suCondition.str_cdt_value=sBeginTm;
	suCondition.e_cdt_type		=CDT_TYPE_GREATER_EQUAL;
	suCondition.e_cdt_val_type  =FD_TYPE_CHAR;
	suCondition.e_cdt_logical   =CDT_LOGIAL_AND;
	suParam.lst_condition.push_back(suCondition);
	
	// 添加调试日志：开始时间
	sprintf(cDebugLog,"[__GetHisCommuStatusDataSet] 设备%d查询条件 - 开始时间: %s", m_nIedId, sBeginTm.c_str());
	RcdTrcLogWithParentClass(cDebugLog,"TNXEcProAsdu17FJ");
	
	// 添加调试日志：查询条件
	sprintf(cDebugLog,"[__GetHisCommuStatusDataSet] 设备%d查询条件 - 设备ID: %d", m_nIedId, m_nIedId);
	RcdTrcLogWithParentClass(cDebugLog,"TNXEcProAsdu17FJ");
	
	//条件2-2 时间范围
	CCTime.SetTime(m_AsduTime.nInfoRcvUtc,m_AsduTime.nInfoRcvMs);
	CCTime.GetStandStringTime(sEndTm);
	suCondition.str_cdt_name="curvaluetm";
	suCondition.str_cdt_value=sEndTm;
	suCondition.e_cdt_type		=CDT_TYPE_SMALLER_EQUAL;
	suCondition.e_cdt_val_type  =FD_TYPE_CHAR;
	suCondition.e_cdt_logical   =CDT_LOGIAL_AND;
	suParam.lst_condition.push_back(suCondition);
	//查询
	pModelMgr=m_pModelSeek->GetModelMgrObj();	
	if (NULL == pModelMgr)
	{
		RcdErrLogWithParentClass("[__GetHisCommuStatusDataSet] 模型管理对象指针失败.","TNXEcProAsdu17FJ");
		return EC_PRO_CVT_FAIL;
	}
	int nRet=pModelMgr->dbm_select_records(&suParam,RcdSet,false,cDbError);
	if(nRet!=0)
	{
		_clear_db_opera_param(suParam);  // 清除查询信息列表
		sprintf(cError,"查询设备%d的通信状态历史数据时出错[%s]",m_nIedId,cDbError);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu17FJ");
		return -1;
	}
	_clear_db_opera_param(suParam);  // 清除查询信息列表
	
	// 添加成功日志
	UINT nRecordNum = 0;
	RcdSet.get_record_num(nRecordNum);
	char cSuccessLog[255];
	sprintf(cSuccessLog,"[__GetHisCommuStatusDataSet] 成功查询设备%d的通信状态历史数据，记录数量=%d", m_nIedId, nRecordNum);
	RcdTrcLogWithParentClass(cSuccessLog,"TNXEcProAsdu17FJ");
	
	// 添加查询结果详情日志
	if (nRecordNum > 0)
	{
		RcdSet.move_to_top();
		string sValue;
		for (int i = 0; i < nRecordNum && i < 5; i++) // 只显示前5条记录
		{
			RcdSet.get_field_value(1, sValue); // cmmustat
			string sCmmuStat = sValue;
			RcdSet.get_field_value(2, sValue); // curvaluetm
			string sCurValueTm = sValue;
			RcdSet.get_field_value(3, sValue); // chgreason_obj
			string sChgReason = sValue;
			
			sprintf(cSuccessLog,"[__GetHisCommuStatusDataSet] 记录%d: 通信状态=%s, 时间=%s, 变化原因=%s", 
				i+1, sCmmuStat.c_str(), sCurValueTm.c_str(), sChgReason.c_str());
			RcdTrcLogWithParentClass(cSuccessLog,"TNXEcProAsdu17FJ");
			
			RcdSet.move_to_next();
		}
		if (nRecordNum > 5)
		{
			sprintf(cSuccessLog,"[__GetHisCommuStatusDataSet] ... 还有%d条记录未显示", nRecordNum - 5);
			RcdTrcLogWithParentClass(cSuccessLog,"TNXEcProAsdu17FJ");
		}
	}
	
	return 0;
}