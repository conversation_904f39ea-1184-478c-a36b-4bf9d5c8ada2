/**********************************************************************
* NXEcProAsdu10.cpp         author:ml      date:08/11/2025            
*---------------------------------------------------------------------
*  note: ASDU10报文转换处理实现文件                                                                
*  
**********************************************************************/

#include "NXEcProAsdu10_FJ.h"

CCvtDataToXml gCvtXml_10;
/**
* @brief         析构函数
* @param[in]     无 
* @param[out]    无
* @return        无
*/
TNXEcProAsdu10FJ::~TNXEcProAsdu10FJ()
{

}

/**
* @brief         构造函数 
* @param[in]     INXEcSSModelSeek * pSeekIns:模型查询实例
* @param[out]    CLogRecord * pLogRecord:日志对象指针
* @return        无
*/
TNXEcProAsdu10FJ::TNXEcProAsdu10FJ(INXEcSSModelSeek * pSeekIns,CLogRecord * pLogRecord)
	:TNXEcProAsdu10(pSeekIns,pLogRecord)
{
	// 设置类名称
	_SetLogClassName("TNXEcProAsdu10FJ");
	m_pEventMsg = NULL;
	m_pCommonMsg = NULL;
}

/**
* @brief         根据NX通用信息及规约命令生成规约结果列表或根据通用消息生成规约命令
* @param[in]     NX_COMMON_MESSAGE * pMsg :通用信息结构指针
* @param[in][out]PRO_FRAME_BODY_LIST & lCmd:规约命令
* @param[out]    PRO_FRAME_BODY_LIST & lResult :规约信息体列表
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu10FJ::ConvertCommonMsgToPro(NX_COMMON_MESSAGE * pMsg,PRO_FRAME_BODY_LIST & lCmd,PRO_FRAME_BODY_LIST & lResult)
{
	char cError[255] = "";
	int  nRet = EC_PRO_CVT_FAIL ;
	m_pCommonMsg = pMsg;

	switch(pMsg->n_msg_type)
	{
	case NX_IED_CALL_SG_REP:       // 定值
		nRet = _CvtSgResultToPro( lCmd,lResult );
		break;
	case NX_IED_CALL_SGZONE_REP:   // 定值区号
		nRet = _CvtZoneResultToPro(lCmd,lResult );
		break;
	case NX_IED_CALL_SOFTSTRAP_REP:// 软压板
		nRet = _CvtSoftResultToPro(lCmd,lResult );
		break;
	case NX_IED_CALL_ANALOG_REP:   // 模拟量
		nRet = _CvtAiResultToPro(lCmd,lResult );
		break;
	case NX_IED_CTRL_SG_CHECK_REP: // 定值预校
		nRet = _CvtSgCheckResultToPro(lCmd,lResult );
		break;
	case NX_IED_CTRL_SG_EXC_REP:   // 定值执行
		nRet = _CvtSgExcResultToPro(lCmd,lResult);
		break;
	case NX_IED_CTRL_SGZONE_CHECK_REP: // 定值区预校
		nRet = _CvtZoneCheckResultToPro(lCmd,lResult);
		break;
	case NX_IED_CTRL_SGZONE_EXC_REP:   // 定值区执行
		nRet = _CvtZoneExcResultToPro(lCmd,lResult);
		break;
	case NX_IED_CTRL_SOFTSTRAP_CHECK_REP: // 软压板预校
		nRet = _CvtSoftCheckResultToPro(lCmd,lResult);
		break;
	case NX_IED_CTRL_SOFTSTRAP_EXC_REP:   // 软压板执行
		nRet = _CvtSoftExcResultToPro(lCmd,lResult);
		break;
	case NX_IED_CTRL_HARDSTRAP_CHECK_REP: // 硬压板预校
		nRet = _CvtHardCheckResultToPro(lCmd,lResult);
		break;
	case NX_IED_CTRL_HARDSTRAP_EXC_REP:   // 硬压板执行
		nRet = _CvtHardExcResultToPro(lCmd,lResult);
		break;
	case NX_IED_CALL_ROBOTCHECK_REP:   // 机器人巡视执行回复
		nRet = _CvtRobertCheckResultToPro(lCmd,lResult);
		break;
	case NX_IED_CALL_61850SRV_READ_REP:
		nRet = _Cvt61850ReadResultToPro(lCmd,lResult);
		break;
	default:
		sprintf(cError,"ConvertCommonMsgToPro()中暂不支持n_msg_type=%d的消息处理",pMsg->n_msg_type);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10");
		nRet = EC_PRO_CVT_NOSUPPORT;
		break;
	}
	m_pCommonMsg = NULL;
	return nRet;
}
/**
* @brief         转换NX软压板变位事件信息到规约事件列表
* @param[out]    PRO_FRAME_BODY_LIST  & lBody :规约信息体
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu10FJ::_CvtSoftReportToPro(PRO_FRAME_BODY_LIST & lBody)
{
	// 转换NX消息为ASDU10Info结构

	char cError[255]="";

	ASDU10_INFO Asdu10Info;
	int nCpu = 0;
	int nAddr = 0;
	int nRet = __CvtNxSubFieldToGenList_IntAuToUp(Asdu10Info.GroupToGenListMap,nCpu,nAddr);

	sprintf(cError,"_CvtSoftReportToPro():获取到的103地址为：%d,CPU为：%d.",
		nAddr,nCpu);
	RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");

	__CvtAsdu10InfoToProBodyAuToUp(nRet,nCpu,nAddr,Asdu10Info,lBody);

	// 清理ASDU10结构
	ClearAsdu10Info(Asdu10Info);
	return 0;
}

/**
* @brief         转换NX定值区变化事件信息到规约事件列表
* @param[out]    PRO_FRAME_BODY_LIST  & lBody :规约信息体
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu10FJ::_CvtZoneChgReportToPro(PRO_FRAME_BODY_LIST & lBody)
{
	// 转换NX消息为ASDU10Info结构
	char cError[255]="";
	ASDU10_INFO Asdu10Info;
	int nCpu = 0;
	int nAddr = 0;
	int nRet = __CvtNxSubFieldToGenList_IntAuToUp(Asdu10Info.GroupToGenListMap,nCpu,nAddr);

	sprintf(cError,"_CvtZoneChgReportToPro():获取到的103地址为：%d,CPU为：%d.",
		nAddr,nCpu);
	RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");

	__CvtAsdu10InfoToProBodyAuToUp(nRet,nCpu,nAddr,Asdu10Info,lBody);

	// 清理ASDU10结构
	ClearAsdu10Info(Asdu10Info);
	return 0;
}
/**
* @brief         转换NX机器人巡视报告事件信息到规约事件列表
* @param[out]    PRO_FRAME_BODY_LIST  & lBody :规约信息体
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu10FJ::_CvtRobertCheckReportToPro(PRO_FRAME_BODY_LIST & lBody)
{
	// 转换NX消息为ASDU10Info结构
	char cError[255]="";
	ASDU10_INFO Asdu10Info;
	int nCpu = 0;
	int nAddr = 0;
	int nRet = __CvtNxSubFieldToGenList_IntAuToUp_Robert(Asdu10Info.GroupToGenListMap,nCpu,nAddr);

	sprintf(cError,"_CvtRobertCheckReportToPro():获取到的103地址为：%d,CPU为：%d.",
		nAddr,nCpu);
	RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");

	__CvtAsdu10InfoToProBodyAuToUp_Rot(nRet,nCpu,nAddr,Asdu10Info,lBody);

	// 清理ASDU10结构
	ClearAsdu10Info(Asdu10Info);
	return 0;
}
/**
* @brief         转换NX通用消息子集数据（值为整型）为组号与通用分类数据集映射表
* @param[out]     GROUP2GENLIST_MAP & GToGenListMap:通用分类数据集映射表
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu10FJ::__CvtNxSubFieldToGenList_IntAuToUp(GROUP2GENLIST_MAP & GToGenListMap,int& nCpu,int& nAddr)
{
	char cError[255]="";
	ASDU_GEN_INFO GenData;     // 通用分类数据结构
	ASDU_GEN_INFO_LIST * pGenList = NULL;
	u_int8 nGroup = 0,nEntry = 0,nDataType=0,nKod=0;
	bool  bGetCfg = false,bDpi = false;
	int nValue = 0;
	const IED_TB * pIed = NULL;
	// 转换NX事件子集结构为通用分类数据
	NX_EVENT_MSG_SUBFILED_LIST::iterator ite = m_pEventMsg->list_subfields.begin();
	while ( ite != m_pEventMsg->list_subfields.end() )
	{
		GenData.vGid.clear();

		nCpu = ite->n_sub_obj_id;

		pIed =  m_pModelSeek->GetIedBasicCfgByID(ite->n_obj_id);
		if( pIed == NULL )
		{
			++ite;
			continue;
		}
		nAddr = pIed->n_outaddr103;

		sprintf(cError,"__CvtNxSubFieldToGenList_Int():获取到的103地址为：%d,CPU为：%d.",
			nAddr,nCpu);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");

		// 获得组号\条目号及数据类型信息
		if(  m_pEventMsg->n_msg_type == NX_SYS_EVENT_IED_SGZONE_CHG_REPORT )
		{
			bGetCfg = ___GetZoneDataPoint103CfgAutoUp(ite->n_obj_id,ite->n_sub_obj_id,ite->n_sub_sub_obj_id,nGroup,nEntry,nDataType);
			nValue = ite->n_value;
		}
		else if( m_pEventMsg->n_msg_type == NX_IED_EVENT_SOFTTRAP_REPORT )
		{
			bDpi = true;
			bGetCfg = ___GetSoftDataPoint103CfgAutoUp(ite->n_obj_id,ite->n_sub_obj_id,ite->n_sub_sub_obj_id,nGroup,nEntry,nDataType);
			if( ite->n_value == 1 )
				nValue = 2;
			else
				nValue = 1;
		}
		else
		{
			sprintf(cError,"__CvtNxSubFieldToGenList_Int():不支持获取n_msg_type=%d消息的103点配置",
				m_pEventMsg->n_msg_type);
			RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
		}
		if( !bGetCfg)
		{
			++ite;
			continue;
		}
		nKod = ___GetGenKod(m_pEventMsg->n_data_src);
		__SetGenericIntData(nGroup,nEntry,nKod,nValue,GenData,bDpi);
		// 将通用分类数据加入列表
		if( GToGenListMap.count(nGroup) > 0 )    // 该组已存在则获取对应的列表指针
		{
			pGenList = GToGenListMap[nGroup];
		}
		else      // 不存在需新建
		{
			pGenList = new ASDU_GEN_INFO_LIST;
			GToGenListMap[nGroup] = pGenList;
		}

		if( pGenList != NULL )
			pGenList->push_back(GenData);

		int nflag = -99;

		if (nflag != nGroup)
		{
			sprintf(cError,"__CvtNxSubFieldToGenList_Int():增加0号条目,组号=%d,nflag=%d.",
				nGroup,nflag);
			RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");

			//加入第0条目,时间信息.
			//装置时间
			string strDevTm;
			CTimeConvert DevTmCvt(ite->n_curvalueutctm,ite->n_curms);
			DevTmCvt.GetCP56TIMe(strDevTm);
			//子站接收时间
			time_t nowtime = time(NULL);
			string strRevTm;
			CTimeConvert RevTmCvt(nowtime,0);
			RevTmCvt.GetCP56TIMe(strRevTm);
			string strTime14 = strDevTm + strRevTm;
			__SetGenericTimeData(nGroup,0,1,strTime14,GenData);
			pGenList->push_front(GenData);

			nflag = nGroup;
		}

		++ite;
		pGenList = NULL;
		nGroup = nEntry = nKod = nDataType = 0;
		bDpi  = false;
		bGetCfg = false;
		nValue = 0;
	}

	sprintf(cError,"__CvtNxSubFieldToGenList_Int():获取到的103地址为：%d,CPU为：%d.",
		nAddr,nCpu);
	RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");

	GenData.vGid.clear();
	return EC_PRO_CVT_SUCCESS;
}
/**
* @brief         转换NX通用消息子集数据（机器人专用）为组号与通用分类数据集映射表
* @param[out]     GROUP2GENLIST_MAP & GToGenListMap:通用分类数据集映射表
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu10FJ::__CvtNxSubFieldToGenList_IntAuToUp_Robert(GROUP2GENLIST_MAP & GToGenListMap,int& nCpu,int& nAddr)
{
	char cError[255]="";
	ASDU_GEN_INFO GenData;     // 通用分类数据结构
	ASDU_GEN_INFO_LIST * pGenList = NULL;
	u_int8 nGroup = 0,nEntry = 0,nDataType=0,nKod=0;
	bool  bGetCfg = false,bDpi = false;
	char cValue[255]="";
	const IED_TB * pIed = NULL;
	// 转换NX事件子集结构为通用分类数据
	NX_EVENT_MSG_SUBFILED_LIST::iterator ite = m_pEventMsg->list_subfields.begin();
	while ( ite != m_pEventMsg->list_subfields.end() )
	{
		GenData.vGid.clear();

		nCpu = 1;

		pIed =  m_pModelSeek->GetIedBasicCfgByID(m_pEventMsg->n_event_obj);
		if( pIed == NULL )
		{
			++ite;
			continue;
		}
		nAddr = pIed->n_outaddr103;

		sprintf(cError,"__CvtNxSubFieldToGenList_IntAuToUp_Robert():获取到的103地址为：%d,CPU为：%d.",
			nAddr,nCpu);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");

		// 获得组号\条目号及数据类型信息
		bGetCfg = ___GetGroupCfg(ite->c_value_f,nGroup,nEntry);
		sprintf(cValue,"%s",ite->c_field_name);
		printf("@@@@@@@@@@@@@@@@@@机器人巡视的装置：%s,文件：%s.\n",ite->c_value_f,ite->c_field_name);
		printf("@@@@@@@@@@@@@@@@@@组号：%d,条目号：%d.\n",nGroup,nEntry);
		if( !bGetCfg)
		{
			++ite;
			continue;
		}
		printf("--------------------------->\n");
		nKod = 1;
		__SetGenericStrData(nGroup,nEntry,nKod,cValue,GenData);
		// 将通用分类数据加入列表
		if( GToGenListMap.count(nGroup) > 0 )    // 该组已存在则获取对应的列表指针
		{
			pGenList = GToGenListMap[nGroup];
		}
		else      // 不存在需新建
		{
			pGenList = new ASDU_GEN_INFO_LIST;
			GToGenListMap[nGroup] = pGenList;
		}

		if( pGenList != NULL )
			pGenList->push_back(GenData);

		++ite;
		pGenList = NULL;
		nGroup = nEntry = nKod = nDataType = 0;
		bDpi  = false;
		bGetCfg = false;
	}

	sprintf(cError,"__CvtNxSubFieldToGenList_IntAuToUp_Robert():获取到的103地址为：%d,CPU为：%d.",
		nAddr,nCpu);
	RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");

	GenData.vGid.clear();
	return EC_PRO_CVT_SUCCESS;
}

/**
* @brief         根据ASDU10信息及召唤命令产生为规约报文体格式列表
* @param[in]     nCvtResult:转换结果 成功或失败
* @param[in]     ASDU10_INFO &Asdu10Info:asdu10信息结构
* @param[in]    PRO_FRAME_BODY_LIST& lCmd:命令列表
* @param[out]    PRO_FRAME_BODY_LIST& lResult:报文体结果列表
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu10FJ::__CvtAsdu10InfoToProBodyAuToUp(int nCvtResult,int nCpu,int nAddr,ASDU10_INFO & Asdu10Info,PRO_FRAME_BODY_LIST & lResult)
{
	char cError[255] = "";
	// 打印日志
	string strLog="转换NX消息：" + _get_eventmsg_desc(*m_pEventMsg) + "到asdu10结构完毕";

	Asdu10Info.Addr.nCpu = nCpu;
	Asdu10Info.Addr.nAddr = nAddr;
	if( Asdu10Info.GroupToGenListMap.size() > 0 )
	{
		strLog += ",结果如下:\n";
		sprintf(cError,"addr =%d,cpu =%d",Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu);
		strLog += cError;
		GROUP2GENLIST_MAP::iterator iteMap = Asdu10Info.GroupToGenListMap.begin();
		while(iteMap != Asdu10Info.GroupToGenListMap.end() )
		{
			_ZERO_MEM(cError,255);
			sprintf(cError,"组号(%d)带%d个条目;",iteMap->first,iteMap->second->size());
			strLog += cError;
			++iteMap;
		}
	}

	RcdTrcLogWithParentClass(strLog.c_str(),"TNXEcProAsdu10FJ");

	// 将ASDU10信息结构格式为ASDU10规约格式
	// 根据消息类型选择对应的RII值
	u_int8 nRii = 0;  // 默认RII值为0（保持原有行为）
	if (m_pEventMsg != NULL) {
		switch(m_pEventMsg->n_msg_type) {
			case NX_IED_EVENT_SOFTTRAP_REPORT:         // 故障量信息
				nRii = 0xFD;  // 故障量信息帧 RII = 253 (FDH)
				break;
			case NX_SYS_EVENT_IED_SGZONE_CHG_REPORT:   // 定值区变化信息
				nRii = 0xFE;  // 定值区变化信息帧 RII = 254 (FEH)
				break;
			default:
				nRii = 0;     // 其他消息类型使用默认RII=0
				break;
		}
	}

	FormatAsdu10BodyAutoUp(Asdu10Info,lResult,nRii);

	_ZERO_MEM(cError,255);
	sprintf(cError,"NX消息结果:%s 转换为%d条asdu10",_get_eventmsg_desc(*m_pEventMsg).c_str(),lResult.size());
	RcdTrcLogWithParentClass(cError,"TNXEcProAsdu10FJ");

	return 0;
}
/**
* @brief         根据ASDU10信息及召唤命令产生为规约报文体格式列表
* @param[in]     nCvtResult:转换结果 成功或失败
* @param[in]     ASDU10_INFO &Asdu10Info:asdu10信息结构
* @param[in]    PRO_FRAME_BODY_LIST& lCmd:命令列表
* @param[out]    PRO_FRAME_BODY_LIST& lResult:报文体结果列表
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu10FJ::__CvtAsdu10InfoToProBodyAuToUp_Rot(int nCvtResult,int nCpu,int nAddr,ASDU10_INFO & Asdu10Info,PRO_FRAME_BODY_LIST & lResult)
{
	char cError[255] = "";
	// 打印日志
	string strLog="转换NX消息：" + _get_eventmsg_desc(*m_pEventMsg) + "到asdu10结构完毕";

	Asdu10Info.Addr.nCpu = nCpu;
	Asdu10Info.Addr.nAddr = nAddr;
	if( Asdu10Info.GroupToGenListMap.size() > 0 )
	{
		strLog += ",结果如下:\n";
		sprintf(cError,"addr =%d,cpu =%d",Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu);
		strLog += cError;
		GROUP2GENLIST_MAP::iterator iteMap = Asdu10Info.GroupToGenListMap.begin();
		while(iteMap != Asdu10Info.GroupToGenListMap.end() )
		{
			_ZERO_MEM(cError,255);
			sprintf(cError,"组号(%d)带%d个条目;",iteMap->first,iteMap->second->size());
			strLog += cError;
			++iteMap;
		}
	}

	RcdTrcLogWithParentClass(strLog.c_str(),"TNXEcProAsdu10FJ");

	// 将ASDU10信息结构格式为ASDU10规约格式

	FormatAsdu10BodyAutoUp_Rot(Asdu10Info,lResult);

	_ZERO_MEM(cError,255);
	sprintf(cError,"NX消息结果:%s 转换为%d条asdu10",_get_eventmsg_desc(*m_pEventMsg).c_str(),lResult.size());
	RcdTrcLogWithParentClass(cError,"TNXEcProAsdu10FJ");

	return 0;
}
/**
* @brief         根据ASDU10信息结构格式化ASDU10报文体
* @param[in]     ASDU10_INFO &Asdu10Info:asdu10信息结构
* @param[out]    PRO_FRAME_BODY_LIST  & lBody :ASDU规约信息体列表
* @param[in]     int nReserve:备用
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu10FJ::FormatAsdu10BodyAutoUp(ASDU10_INFO &Asdu10Info,PRO_FRAME_BODY_LIST &lBody,int nReserve)
{
	char cError[255] = "";
	int nSrcSize = lBody.size();
	int nEntryNum = 0;  // 总条目数
	GROUP2GENLIST_MAP::iterator iteMap;
	ASDU_GEN_INFO_LIST * pGenList= NULL;


	// 格式化所有组自动上送
	iteMap = Asdu10Info.GroupToGenListMap.begin();
	while( iteMap != Asdu10Info.GroupToGenListMap.end() )
	{
		_FormatOneGroupAsdu10BodyAutoUp(Asdu10Info,iteMap->first,0,lBody);
		pGenList = iteMap->second;
		if( pGenList != NULL )
			nEntryNum += pGenList->size();
		++iteMap;
	}
	sprintf(cError,"FormatAsdu10BodyAutoUp():生成%d条自动上送asdu10,addr=%d cpu=%d 总条目数=%d ",
		lBody.size()-nSrcSize,Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu,nEntryNum);

	RcdTrcLogWithParentClass(cError,"TNXEcProAsdu10FJ");
	return 0;
}
/**
* @brief         根据ASDU10信息结构格式化ASDU10报文体
* @param[in]     ASDU10_INFO &Asdu10Info:asdu10信息结构
* @param[out]    PRO_FRAME_BODY_LIST  & lBody :ASDU规约信息体列表
* @param[in]     int nReserve:备用
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu10FJ::FormatAsdu10BodyAutoUp_Rot(ASDU10_INFO &Asdu10Info,PRO_FRAME_BODY_LIST &lBody,int nReserve)
{
	char cError[255] = "";
	int nSrcSize = lBody.size();
	int nEntryNum = 0;  // 总条目数
	GROUP2GENLIST_MAP::iterator iteMap;
	ASDU_GEN_INFO_LIST * pGenList= NULL;


	// 格式化所有组自动上送
	iteMap = Asdu10Info.GroupToGenListMap.begin();
	while( iteMap != Asdu10Info.GroupToGenListMap.end() )
	{
		_FormatOneGroupAsdu10BodyAutoUp_Rot(Asdu10Info,iteMap->first,0,lBody);
		pGenList = iteMap->second;
		if( pGenList != NULL )
			nEntryNum += pGenList->size();
		++iteMap;
	}
	sprintf(cError,"FormatAsdu10BodyAutoUp():生成%d条自动上送asdu10,addr=%d cpu=%d 总条目数=%d ",
		lBody.size()-nSrcSize,Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu,nEntryNum);

	RcdTrcLogWithParentClass(cError,"TNXEcProAsdu10FJ");
	return 0;
}
/**
* @brief         根据ASDU10信息结构格式化指定组号的ASDU10报文体
* @param[in]     ASDU10_INFO &Asdu10Info:asdu10信息结构
* @param[in]     u_int8 nGroup:指定组号
* @param[in]     u_int8 nRii:返回信息标识符
* @param[out]    PRO_FRAME_BODY_LIST  & lBody :ASDU规约信息体列表
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu10FJ::_FormatOneGroupAsdu10BodyAutoUp(ASDU10_INFO &Asdu10Info,u_int8 nGroup,u_int8 nRii,PRO_FRAME_BODY_LIST &lBody)
{
	char cError[255] = "";
	string strLog;
	u_int8 nNgd = 0;            // 通用分类数据集数目
	u_int8 nCounter=0,nTmpValue=0; // 控制计数器位
	PRO_FRAME_BODY ProBody;     // 规约报文体
	ASDU_GEN_INFO_LIST * pGenericList=NULL;
	// 获取通用分类数据集列表
	if( Asdu10Info.InfoObj.nInf == 0xf0 )  //组标题回应
	{
		GROUP2GENLIST_MAP::iterator iteMap = Asdu10Info.GroupToGenListMap.begin();
		pGenericList = iteMap->second;
	}
	else
		pGenericList = Asdu10Info.GroupToGenListMap[nGroup];
	if( pGenericList == NULL )
	{
		sprintf(cError,"_FormatOneGroupAsdu10BodyAutoUp():stationAddr =%d addr=%d cpu=%d inf=%d的asdu10信息结构中group=%d的数据集列表为NULL,生成asdu10报文体失败",
			Asdu10Info.Addr.nSubstationAdd,Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu,Asdu10Info.InfoObj.nInf,nGroup);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
		return EC_PRO_CVT_FAIL;
	}
	// 判断数据集个数
	int nGenSize = pGenericList->size();
	if( nGenSize <= 0 )
	{
		sprintf(cError,"_FormatOneGroupAsdu10BodyAutoUp():stationAddr =%d addr=%d cpu=%d inf=%d的asdu10信息结构中group=%d的数据集个数为%d,生成asdu10报文体失败",
			Asdu10Info.Addr.nSubstationAdd,Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu,Asdu10Info.InfoObj.nInf,nGroup,nGenSize);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
		return EC_PRO_CVT_FAIL;
	}

	// 获得变电站地址
// 	const SUBSTATION_TB * pStation = m_pModelSeek->GetSubStationBasicCfg();
// 	if( pStation == NULL )
// 		return EC_PRO_CVT_FAIL;
// 	int16 nSubstationAdd = pStation->n_outaddr103;

	// 设置规约帧固定部分
	ProBody.nType =  0x0A;   
	ProBody.nVsq  =  0x81;    
	ProBody.nCot  =  0x01;
	ProBody.nSubstationAdd = Asdu10Info.Addr.nSubstationAdd;
// 	ProBody.nAddr = Asdu10Info.Addr.nAddr;
// 	ProBody.nCpu  = Asdu10Info.Addr.nCpu;
	ProBody.nAddr = Asdu10Info.Addr.nAddr;
	ProBody.nCpu  = Asdu10Info.Addr.nCpu;
	ProBody.nZone = Asdu10Info.Addr.nZone;
	ProBody.nFun  = 0xFE;//m_nFun;
	ProBody.nInf  = 0xF1;//m_nInf;

	// 设置可变部分
	ProBody.vVarData.push_back(nRii);          // 返回信息标识符
	ProBody.vVarData.push_back(nNgd);          // NGD (暂时加入，后续根据条目数情况再修改)
	ASDU_GEN_INFO_LIST::iterator ite = pGenericList->begin();
	while( ite != pGenericList->end() )
	{
		// 判断可变部分是否越界
		if( ( (ProBody.vVarData.size()+ 6 + ite->vGid.size() ) > GetAsduVarDataMaxLen() ) || 
			( nNgd >= ASDU10_MAX_NGD ) 
			)
		{
			// 设置计数器位
			nTmpValue = (nCounter & 0x01) << 6; // 取最低位,然后左移6位
			nNgd = nNgd | nTmpValue;
			nCounter ++ ;
			nTmpValue = 0;
			// 设置后续状态位
			nNgd = nNgd | 0x80;
			// 重新设置可变部分的NGD数目
			ProBody.vVarData[1] = nNgd;
			// 加入链表
			lBody.push_back(ProBody);
			// 清除可变部分数据
			ProBody.vVarData[1] = 0;
			ProBody.vVarData.erase(ProBody.vVarData.begin()+2,ProBody.vVarData.end());
			nNgd = 0;
		}
		// 填加数据集
		ProBody.vVarData.push_back(ite->nGroup);
		ProBody.vVarData.push_back(ite->nEntry);
		ProBody.vVarData.push_back(ite->nKod);
		ProBody.vVarData.push_back(ite->nDataType);
		ProBody.vVarData.push_back(ite->nDataSize);
		ProBody.vVarData.push_back(ite->nDataNumber);
		if( ite->vGid.size() > 0 )
			ProBody.vVarData.insert(ProBody.vVarData.end(),ite->vGid.begin(),ite->vGid.end());
		// NGD数目增加
		nNgd ++;
		nGenSize --;
		if ( 0 == nGenSize )  // 最后一帧
		{
			// 设置计数器位
			nTmpValue = (nCounter & 0x01) << 6; // 取最低位,然后左移6位
			nNgd = nNgd | nTmpValue;
			// 重新设置可变部分的NGD数目
			ProBody.vVarData[1] = nNgd;
			// 加入链表
			lBody.push_back(ProBody);
			// 清除可变部分数据
			ProBody.vVarData.clear();
			nNgd = 0;
			break;
		}
		++ite;
	}
	ProBody.vVarData.clear();
	return 0;
}
/**
* @brief         根据ASDU10信息结构格式化指定组号的ASDU10报文体
* @param[in]     ASDU10_INFO &Asdu10Info:asdu10信息结构
* @param[in]     u_int8 nGroup:指定组号
* @param[in]     u_int8 nRii:返回信息标识符
* @param[out]    PRO_FRAME_BODY_LIST  & lBody :ASDU规约信息体列表
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu10FJ::_FormatOneGroupAsdu10BodyAutoUp_Rot(ASDU10_INFO &Asdu10Info,u_int8 nGroup,u_int8 nRii,PRO_FRAME_BODY_LIST &lBody)
{
	char cError[255] = "";
	string strLog;
	u_int8 nNgd = 0;            // 通用分类数据集数目
	u_int8 nCounter=0,nTmpValue=0; // 控制计数器位
	PRO_FRAME_BODY ProBody;     // 规约报文体
	ASDU_GEN_INFO_LIST * pGenericList=NULL;
	// 获取通用分类数据集列表
	if( Asdu10Info.InfoObj.nInf == 0xf0 )  //组标题回应
	{
		GROUP2GENLIST_MAP::iterator iteMap = Asdu10Info.GroupToGenListMap.begin();
		pGenericList = iteMap->second;
	}
	else
		pGenericList = Asdu10Info.GroupToGenListMap[nGroup];
	if( pGenericList == NULL )
	{
		sprintf(cError,"_FormatOneGroupAsdu10BodyAutoUp():stationAddr =%d addr=%d cpu=%d inf=%d的asdu10信息结构中group=%d的数据集列表为NULL,生成asdu10报文体失败",
			Asdu10Info.Addr.nSubstationAdd,Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu,Asdu10Info.InfoObj.nInf,nGroup);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
		return EC_PRO_CVT_FAIL;
	}
	// 判断数据集个数
	int nGenSize = pGenericList->size();
	if( nGenSize <= 0 )
	{
		sprintf(cError,"_FormatOneGroupAsdu10BodyAutoUp():stationAddr =%d addr=%d cpu=%d inf=%d的asdu10信息结构中group=%d的数据集个数为%d,生成asdu10报文体失败",
			Asdu10Info.Addr.nSubstationAdd,Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu,Asdu10Info.InfoObj.nInf,nGroup,nGenSize);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
		return EC_PRO_CVT_FAIL;
	}

	// 获得变电站地址
	// 	const SUBSTATION_TB * pStation = m_pModelSeek->GetSubStationBasicCfg();
	// 	if( pStation == NULL )
	// 		return EC_PRO_CVT_FAIL;
	// 	int16 nSubstationAdd = pStation->n_outaddr103;

	// 设置规约帧固定部分
	ProBody.nType =  0x0A;   
	ProBody.nVsq  =  0x81;    
	ProBody.nCot  =  0x01;
	ProBody.nSubstationAdd = Asdu10Info.Addr.nSubstationAdd;
	// 	ProBody.nAddr = Asdu10Info.Addr.nAddr;
	// 	ProBody.nCpu  = Asdu10Info.Addr.nCpu;
	ProBody.nAddr = Asdu10Info.Addr.nAddr;
	ProBody.nCpu  = Asdu10Info.Addr.nCpu;
	ProBody.nZone = Asdu10Info.Addr.nZone;
	ProBody.nFun  = 0xFE;//m_nFun;
	ProBody.nInf  = 0xF2;//m_nInf;

	// 设置可变部分
	ProBody.vVarData.push_back(nRii);          // 返回信息标识符
	ProBody.vVarData.push_back(nNgd);          // NGD (暂时加入，后续根据条目数情况再修改)
	ASDU_GEN_INFO_LIST::iterator ite = pGenericList->begin();
	while( ite != pGenericList->end() )
	{
		// 判断可变部分是否越界
		if( ( (ProBody.vVarData.size()+ 6 + ite->vGid.size() ) > GetAsduVarDataMaxLen() ) || 
			( nNgd >= ASDU10_MAX_NGD ) 
			)
		{
			// 设置计数器位
			nTmpValue = (nCounter & 0x01) << 6; // 取最低位,然后左移6位
			nNgd = nNgd | nTmpValue;
			nCounter ++ ;
			nTmpValue = 0;
			// 设置后续状态位
			nNgd = nNgd | 0x80;
			// 重新设置可变部分的NGD数目
			ProBody.vVarData[1] = nNgd;
			// 加入链表
			lBody.push_back(ProBody);
			// 清除可变部分数据
			ProBody.vVarData[1] = 0;
			ProBody.vVarData.erase(ProBody.vVarData.begin()+2,ProBody.vVarData.end());
			nNgd = 0;
		}
		// 填加数据集
		ProBody.vVarData.push_back(ite->nGroup);
		ProBody.vVarData.push_back(ite->nEntry);
		ProBody.vVarData.push_back(ite->nKod);
		ProBody.vVarData.push_back(ite->nDataType);
		ProBody.vVarData.push_back(ite->nDataSize);
		ProBody.vVarData.push_back(ite->nDataNumber);
		if( ite->vGid.size() > 0 )
			ProBody.vVarData.insert(ProBody.vVarData.end(),ite->vGid.begin(),ite->vGid.end());
		// NGD数目增加
		nNgd ++;
		nGenSize --;
		if ( 0 == nGenSize )  // 最后一帧
		{
			// 设置计数器位
			nTmpValue = (nCounter & 0x01) << 6; // 取最低位,然后左移6位
			nNgd = nNgd | nTmpValue;
			// 重新设置可变部分的NGD数目
			ProBody.vVarData[1] = nNgd;
			// 加入链表
			lBody.push_back(ProBody);
			// 清除可变部分数据
			ProBody.vVarData.clear();
			nNgd = 0;
			break;
		}
		++ite;
	}
	ProBody.vVarData.clear();
	return 0;
}
/**
* @brief         获得定值区号信息点的103配置
* @param[in]     nGroup:信息点编号
* @param[out]    nGroup:组号
* @param[out]    nItem: 条目号
* @param[out]    nDataType:数据类型
* @return        bool:true-成功 false-失败
*/
bool TNXEcProAsdu10FJ::___GetZoneDataPoint103CfgAutoUp(int nIed,int nLd,int nID,u_int8 &nGroup,u_int8 &nEntry,u_int8 &nDataType)
{
	char cError[255] = "";
	const SGZONE_TB * pTb = NULL;
	pTb = m_pModelSeek->GetIedZoneCfg(nIed,nLd,nID);
	if( pTb == NULL )
	{
		sprintf(cError,"___GetZoneDataPoint103Cfg():获取IedId=%d,LD=%d,ID=%d的配置失败",
			nIed,nLd,nID);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
		return false;
	}
	nGroup = pTb->n_outgroup103;
	nEntry = pTb->n_outitem103;
	nDataType = VALUE_INT;
	return true;
}

/**
* @brief         获得软压板信息点的103配置
* @param[in]     nGroup:信息点编号
* @param[out]    nGroup:组号
* @param[out]    nItem: 条目号
* @param[out]    nDataType:数据类型
* @return        bool:true-成功 false-失败
*/
bool TNXEcProAsdu10FJ::___GetSoftDataPoint103CfgAutoUp(int nIed,int nLd,int nID,u_int8 &nGroup,u_int8 &nEntry,u_int8 &nDataType)
{
	char cError[255] = "";
	const STRAP_TB * pTb = NULL;
	pTb = m_pModelSeek->GetIedSoftStrapCfg(nIed,nLd,nID);
	if( pTb == NULL )
	{
		sprintf(cError,"___GetSoftDataPoint103Cfg():获取IedId=%d,LD=%d,ID=%d的配置失败",
			nIed,nLd,nID);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
		return false;
	}
	nGroup = pTb->n_outgroup103;
	nEntry = pTb->n_outitem103;
	nDataType = VALUE_INT;
	return true;
}
/**
* @brief         根据ASDU10信息和消息类型转换为指定的NX通用消息结构
* @param[in]     int nMsgType:消息类型
* @param[in]     ASDU10_INFO &Asdu10Info:asdu10信息结构
* @param[out]    NX_COMMON_MESSAGE &CommonMsg:通用消息
* @return        int 0-成功 其它失败
*/
//这个函数直接使用基类中的，用这个会出问题
//int TNXEcProAsdu10FJ::__CvtAsdu10InfoStructToCommonMsg(int nMsgType,ASDU10_INFO & Asdu10Info,NX_COMMON_MESSAGE &CommonMsg)
//{
//	char cError[255]="";
//	// 获得地址信息
//	const IED_TB * pIedTb = m_pModelSeek->GetIedBasicCfgByAddr103(Asdu10Info.Addr.nAddr);
//	if( pIedTb == NULL )
//	{
//		return EC_PRO_CVT_FAIL;
//	}
//
//	int nBack = (pIedTb->n_obj_id*1000)+Asdu10Info.Addr.nCpu;
//	int nRealIed,nRealCpu;
//	if (0 !=__DBQueryIedByBack(nBack,nRealIed,nRealCpu))
//	{
//		sprintf(cError,"__CvtAsdu10InfoStructToCommonMsg():获取压板备用字段1[%d]的真实IED失败.",nBack);
//		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
//		return EC_PRO_CVT_FAIL;
//	}
//	
//	CommonMsg.n_msg_topic = NX_TOPIC_COMMAND;
//	CommonMsg.n_msg_type  = nMsgType;
//	CommonMsg.n_obj_id    = nRealIed;
//	CommonMsg.n_obj_type  = NX_OBJ_TYPE_NX_IED;
//	CommonMsg.n_sub_obj_id= nRealCpu;
//	CommonMsg.n_sub_sub_obj_id = Asdu10Info.Addr.nZone;
//	CommonMsg.n_data_src = 2;  // 表示命令来自远方主站
//	CommonMsg.b_lastmsg  = true;
//
//	// 转换NX_SUBFIELDS
//	NX_COMMON_MSG_SUBFILED_LIST * pNxSubList = &CommonMsg.list_subfields;
//	GROUP2GENLIST_MAP::iterator iteMap = Asdu10Info.GroupToGenListMap.begin();
//	ASDU_GEN_INFO_LIST * pGenList = NULL;
//	ASDU_GEN_INFO_LIST::iterator ite;
//	NX_COMMON_FIELD_STRUCT tmpSubField;
//	string strValue;
//	int nID = 0;
//
//	while( iteMap != Asdu10Info.GroupToGenListMap.end() )
//	{
//		pGenList = iteMap->second;
//		if( pGenList == NULL )
//		{
//			++iteMap;
//			continue;
//		}
//		// 遍历通用分类数据集列表
//		ite = pGenList->begin();
//		while (ite != pGenList->end())
//		{
//			tmpSubField.n_field_id = -1;
//			_ZERO_MEM(tmpSubField.c_value,sizeof(tmpSubField.c_value));
//			tmpSubField.n_value = -1;
//			strValue.clear();
//			nID = 0;
//
//			// 将通用分类数据类型转换为字符值
//			strValue = ___CvtGenericValueToStr(*ite);
//
//			// 根据组号、条目号、IED_ID及cpu号获取信息点编号
//			switch( nMsgType )
//			{
//			case NX_IED_CALL_ANALOG_REP:
//				memcpy(tmpSubField.c_value,strValue.c_str(),_MIN_VAL(strValue.size(),sizeof(tmpSubField.c_value)-1));
//				nID = ___GetAiPointIDBy103Info(pIedTb->n_obj_id,Asdu10Info.Addr.nCpu,ite->nGroup,ite->nEntry);
//				break;
//			case NX_IED_CALL_SG_REP:
//			case NX_IED_CTRL_SG_CHECK_ASK:
//			case NX_IED_CTRL_SG_EXC_ASK:
//				memcpy(tmpSubField.c_value,strValue.c_str(),_MIN_VAL(strValue.size(),sizeof(tmpSubField.c_value)-1));
//				nID =___GetSgPointIDBy103Info(pIedTb->n_obj_id,Asdu10Info.Addr.nCpu,ite->nGroup,ite->nEntry);
//				break;
//			case NX_IED_CALL_SGZONE_REP:
//			case NX_IED_CTRL_SGZONE_CHECK_ASK:
//			case NX_IED_CTRL_SGZONE_EXC_ASK:
//				tmpSubField.n_value = atoi(strValue.c_str());
//				nID =___GetZonePointIDBy103Info(pIedTb->n_obj_id,Asdu10Info.Addr.nCpu,ite->nGroup,ite->nEntry);
//				break;
//			case NX_IED_CALL_SOFTSTRAP_REP:
//			case NX_IED_CTRL_SOFTSTRAP_CHECK_ASK:
//			case NX_IED_CTRL_SOFTSTRAP_EXC_ASK:
//				tmpSubField.n_value = atoi(strValue.c_str());
//				nID =___GetSoftPointIDBy103Info(pIedTb->n_obj_id,Asdu10Info.Addr.nCpu,ite->nGroup,ite->nEntry);
//				break;
//			case NX_IED_CALL_HARDSTRAP_REP:
//			case NX_IED_CTRL_HARDSTRAP_CHECK_ASK:
//			case NX_IED_CTRL_HARDSTRAP_EXC_ASK:
//				tmpSubField.n_value = atoi(strValue.c_str());
//				nID =___GetHardPointIDBy103Info(nRealIed,nRealCpu,ite->nGroup,ite->nEntry);
//				break;
//			default:
//				nID = -1;
//				break;
//			}
//			if( nID != -1 )
//			{
//				tmpSubField.n_field_id = nID;
//				// 加入NX子集列表
//				pNxSubList->push_back(tmpSubField);
//			}
//			++ite;
//		} // while (ite != pGenList->end())
//
//		pGenList = NULL;
//		++iteMap;
//	} // while( iteMap != Asdu10Info.GroupToGenListMap.end() )
//
//	return EC_PRO_CVT_SUCCESS;
//}
/**
* @brief        获得硬压板信息点的内部ID编号
* @param[in]    nIedId:设备ID
* @param[in]    nCPU:设备CPU号
* @param[in]    nGroup:组号
* @param[in]    nItem: 条目号
* @return       int :信息点ID ,当获取失败时返回-1
*/
int TNXEcProAsdu10FJ::___GetHardPointIDBy103Info(int nIedId,int nCpu,u_int8 nGroup,u_int8 nEntry)
{
	char cError[255] = "";
	const STRAP_TB * pTb = NULL;
	pTb = m_pModelSeek->GetIedDiCfg(nIedId,nCpu,nGroup,nEntry);
	if( pTb == NULL )
	{
		sprintf(cError,"___GetHardPointIDBy103Info():获取IedId=%d,LD=%d,group=%d entry=%d的配置失败",
			nIedId,nCpu,nGroup,nEntry);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10");
		return -1;
	}

	return pTb->n_strap_code;
}
/**
* @brief         根据ASDU10信息转换为远方控制预校通用消息
* @param[in]     int nGroupType:组标题类型
* @param[in]     ASDU10_INFO &Asdu10Info:asdu10信息结构
* @param[out]    NX_COMMON_MSG_LIST& lMsg:通用消息列表
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu10FJ::_CvtAsdu10InfoToRCtlCheck(int nGroupType,ASDU10_INFO & Asdu10Info,NX_COMMON_MSG_LIST& lMsg)
{
	char cError[255] = "";
	int nRet = EC_PRO_CVT_FAIL;
	switch(nGroupType)
	{
	case EC_GROUP_SG:        // 定值修改
		nRet = _CvtAsdu10ToSgCheck(Asdu10Info,lMsg);
		break;
	case EC_GROUP_SOFT:      // 软压板投退
		nRet = _CvtAsdu10ToSofCheck(Asdu10Info,lMsg);
		break;
	case EC_GROUP_ZONE:      // 区号切换
		nRet = _CvtAsdu10ToZoneCheck(Asdu10Info,lMsg);
		break;
	case EC_GROUP_HARD:      // 硬压板投退
		nRet = _CvtAsdu10ToHardCheck(Asdu10Info,lMsg);
		break;
	default:
		sprintf(cError,"_CvtAsdu10InfoToRCtlCheck()中收到的控制命令组类型为:%d,不支持处理",nGroupType);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
		nRet = EC_PRO_CVT_NOSUPPORT;
		break;
	}
	return nRet;
}
/**
* @brief         根据ASDU10信息转换为远方控制执行通用消息
* @param[in]     int nGroupType:组标题类型
* @param[in]     ASDU10_INFO &Asdu10Info:asdu10信息结构
* @param[out]    NX_COMMON_MSG_LIST& lMsg:通用消息列表
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu10FJ::_CvtAsdu10InfoToRCtlExc(int nGroupType,ASDU10_INFO & Asdu10Info,NX_COMMON_MSG_LIST& lMsg)
{
	char cError[255] = "";
	int nRet = EC_PRO_CVT_FAIL;
	switch(nGroupType)
	{
	case EC_GROUP_SG:        // 定值修改
		nRet = _CvtAsdu10ToSgExc(Asdu10Info,lMsg);
		break;
	case EC_GROUP_SOFT:      // 软压板投退
		nRet = _CvtAsdu10ToSoftExc(Asdu10Info,lMsg);
		break;
	case EC_GROUP_ZONE:      // 区号切换
		nRet = _CvtAsdu10ToZoneExc(Asdu10Info,lMsg);
		break;
	case EC_GROUP_HARD:      // 硬压板投退
		nRet = _CvtAsdu10ToHardExc(Asdu10Info,lMsg);
		break;
	default:
		sprintf(cError,"_CvtAsdu10InfoToRCtlExc()中收到的控制命令组类型为:%d,不支持处理",nGroupType);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
		nRet = EC_PRO_CVT_NOSUPPORT;
		break;
	}
	return nRet;
}
/**
* @brief         根据ASDU10信息转换为NX硬压板投退预校命令
* @param[in]     ASDU10_INFO &Asdu10Info:asdu10信息结构
* @param[out]    NX_COMMON_MSG_LIST& lMsg:通用消息列表
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu10FJ::_CvtAsdu10ToHardCheck(ASDU10_INFO & Asdu10Info,NX_COMMON_MSG_LIST& lMsg)
{
	char cError[255]="";
	NX_COMMON_MESSAGE CommonMsg;

	int nRet = __CvtAsdu10InfoStructToCommonMsg(NX_IED_CTRL_HARDSTRAP_CHECK_ASK,Asdu10Info,CommonMsg);
	if( nRet != EC_PRO_CVT_SUCCESS )
		return nRet;

	// 判断转换后生成的NX子集是否大于0
	if( CommonMsg.list_subfields.size() <= 0 )
	{
		sprintf(cError,"_CvtAsdu10ToSofCheck():转换硬压板预校命令(addr=%d cpu=%d)时,没有生成对应的硬压板点信息",
			Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10");
		return EC_PRO_CVT_FAIL;
	}

	// 将生成的通用消息添加到通用消息列表
	lMsg.push_back(CommonMsg);

	sprintf(cError,"_CvtAsdu10ToSofCheck():硬压板预校命令(addr=%d cpu=%d)转换为通用消息成功,生成%d个硬压板点信息",
		Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu,CommonMsg.list_subfields.size());
	RcdTrcLogWithParentClass(cError,"TNXEcProAsdu10");

	m_CsgLogDataUnit.e_log_unit_datatype = CSG_LOG_UNIT_DATATYPE_SOFTBORAD;
	m_CsgLogDataUnit.e_attribute         = CSG_LOG_UNIT_ATTRIBUTE_WRITE;
	m_CsgLogDataUnit.str_sponer    = "主站";
	m_CsgLogDataUnit.e_behavior  = CSG_LOG_UNIT_BEHAVIOR_SETTING_SOLIDIFICATION;
	m_CsgLogDataUnit.str_target    = "子站";
	m_CsgLogDataUnit.str_dataobject = "硬压板投退预校命令.";
	_ZERO_MEM(cError,255);
	sprintf(cError,"addr=%d cpu=%d",Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu);
	m_CsgLogDataUnit.str_dataauxiliary = cError;
	CsgLogOutOpr(m_CsgLogDataUnit);

	CommonMsg.list_subfields.clear();
	return EC_PRO_CVT_SUCCESS;
}
/**
* @brief         根据ASDU10信息转换为NX硬压板投退执行命令
* @param[in]     ASDU10_INFO &Asdu10Info:asdu10信息结构
* @param[out]    NX_COMMON_MSG_LIST& lMsg:通用消息列表
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu10FJ::_CvtAsdu10ToHardExc(ASDU10_INFO & Asdu10Info,NX_COMMON_MSG_LIST& lMsg)
{
	char cError[255]="";
	NX_COMMON_MESSAGE CommonMsg;

	int nRet = __CvtAsdu10InfoStructToCommonMsg(NX_IED_CTRL_HARDSTRAP_EXC_ASK,Asdu10Info,CommonMsg);
	if( nRet != EC_PRO_CVT_SUCCESS )
		return nRet;

	// 将生成的通用消息添加到通用消息列表
	lMsg.push_back(CommonMsg);

	sprintf(cError,"_CvtAsdu10ToSoftExc():硬压板投退执行命令(addr=%d cpu=%d)转换为通用消息成功",
		Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu);
	RcdTrcLogWithParentClass(cError,"TNXEcProAsdu10");

	m_CsgLogDataUnit.e_log_unit_datatype = CSG_LOG_UNIT_DATATYPE_SETTING;
	m_CsgLogDataUnit.e_attribute         = CSG_LOG_UNIT_ATTRIBUTE_WRITE;
	m_CsgLogDataUnit.str_sponer    = "主站";
	m_CsgLogDataUnit.e_behavior  = CSG_LOG_UNIT_BEHAVIOR_SETTING_SOLIDIFICATION;
	m_CsgLogDataUnit.str_target    = "子站";
	m_CsgLogDataUnit.str_dataobject = "硬压板投退执行命令.";
	_ZERO_MEM(cError,255);
	sprintf(cError,"addr=%d cpu=%d",Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu);
	m_CsgLogDataUnit.str_dataauxiliary = cError;
	CsgLogOutOpr(m_CsgLogDataUnit);

	CommonMsg.list_subfields.clear();
	return EC_PRO_CVT_SUCCESS;
}
/*****************************************************************
* @brief         根据开关量从数据库中查找指定设备ID.
* @param[in]     int nIed_Id:设备ID.
* @param[out]    INT nStation_Id: 厂站ID.
* @return        int 0-成功 其它失败
****************************************************************/
int TNXEcProAsdu10FJ::__DBQueryIedByBack(int &nBack,int & nIed,int & nCpu)
{
	INXEcModelMgr * pModelMgr;
	DB_OPER_PARAM suParam;				 // 数据库查询参数结构体
	DB_FIELD_DATA suField;               //字段结构体
	DB_CONDITION  suCondition;           //条件结构体
	CPlmRecordSet RcdSet;				 //结果数据集

	char cDBError[255]="";
	char cLogMsg[500]="";

	//要查询出的连个字段
	suField.str_fd_name="ied_obj";			//IED
	suParam.lst_fddata.push_back(suField);
	//要查询出的连个字段
	suField.str_fd_name="ld_code";			//CPU
	suParam.lst_fddata.push_back(suField);


	//查询表名
	suParam.lst_tablename.push_back("nx_t_ied_hardstrap_cfg");

	//查询条件1  设备ID
	suCondition.str_cdt_name="strbackup1";
	char cIed_Id[50]="";
	sprintf(cIed_Id,"%d",nBack);
	suCondition.str_cdt_value=cIed_Id;
	suCondition.e_cdt_type		=CDT_TYPE_EQUAL;
	suCondition.e_cdt_val_type  =FD_TYPE_NUMERIC;
	suParam.lst_condition.push_back(suCondition);

	//查询nx_t_ied_comtrade_list历史录波列表中,是否存在符合指定条件的记录.
	pModelMgr=m_pModelSeek->GetModelMgrObj();	
	int nRet=pModelMgr->dbm_select_records(&suParam,RcdSet,false,cDBError);
	if(nRet!=0)
	{
		_clear_db_opera_param(suParam); // 清除查询信息列表
		sprintf(cLogMsg,"__DBQueryIedByBack():全站查询nBack为%d的信息时出错[%s]",nBack,cDBError);
		RcdErrLogWithParentClass(cLogMsg,"TNXEcProAsdu10FJ");
		return EC_PRO_CVT_FAIL;
	}

	_clear_db_opera_param(suParam); // 清除查询信息列表

	//检查结果
	UINT nRecordNum=0;
	RcdSet.get_record_num(nRecordNum);
	if(0==nRecordNum)//没有找到对应文件
	{
		RcdSet.clear_result();
		memset(cLogMsg,0,500);
		sprintf(cLogMsg,"__DBQueryIedByBack():全站的ied中,nBack为%d的配置不存在.",nBack);
		RcdErrLogWithParentClass(cLogMsg,"TNXEcProAsdu10FJ");
		return EC_PRO_CVT_FAIL;
	}
	string strValue;
	RcdSet.get_field_value_row(1,1,strValue);
	nIed = atoi(strValue.c_str());

	RcdSet.get_field_value_row(2,1,strValue);
	nCpu = atoi(strValue.c_str());

	RcdSet.clear_result();
	memset(cLogMsg,0,500);
	sprintf(cLogMsg,"__DBQueryIedByBack():获取nBack：%d,nIed：%d.nCpu：%d.",nBack,nIed,nCpu);
	RcdTrcLogWithParentClass(cLogMsg,"TNXEcProAsdu10FJ");
	return EC_PRO_CVT_SUCCESS;
}
/*****************************************************************
	* @brief         根据定值名称从数据库中查找对应组号条目号.
	* @param[in]     string strSgName:定值名称.
	* @param[out]    INT nGroup: 组号.
	* @param[out]    INT nEntry: 条目号.
	* @return        bool true-成功 false-失败
	****************************************************************/
bool TNXEcProAsdu10FJ::___GetGroupCfg(char *cSgName,u_int8 & nGroup,u_int8 & nEntry)
{
	INXEcModelMgr * pModelMgr;
	DB_OPER_PARAM suParam;				 // 数据库查询参数结构体
	DB_FIELD_DATA suField;               //字段结构体
	DB_CONDITION  suCondition;           //条件结构体
	CPlmRecordSet RcdSet;				 //结果数据集

	char cDBError[255]="";
	char cLogMsg[500]="";

	//要查询出的连个字段
	suField.str_fd_name="outgroup103";			//IED
	suParam.lst_fddata.push_back(suField);
	//要查询出的连个字段
	suField.str_fd_name="outitem103";			//CPU
	suParam.lst_fddata.push_back(suField);


	//查询表名
	suParam.lst_tablename.push_back("nx_t_ied_sg_cfg");

	//查询条件1  设备ID
	suCondition.str_cdt_name="aliasname";
	char cIed_Id[50]="";
	sprintf(cIed_Id,"%s",cSgName);
	suCondition.str_cdt_value=cIed_Id;
	suCondition.e_cdt_type		=CDT_TYPE_EQUAL;
	suCondition.e_cdt_val_type  =FD_TYPE_CHAR;
	suParam.lst_condition.push_back(suCondition);

	//查询nx_t_ied_sg_cfg中,指定定值名称的组号条目号.
	pModelMgr=m_pModelSeek->GetModelMgrObj();	
	int nRet=pModelMgr->dbm_select_records(&suParam,RcdSet,false,cDBError);
	if(nRet!=0)
	{
		_clear_db_opera_param(suParam); // 清除查询信息列表
		sprintf(cLogMsg,"___GetGroupCfg():全站查询cSgName为%s的信息时出错[%s]",cSgName,cDBError);
		RcdErrLogWithParentClass(cLogMsg,"TNXEcProAsdu10FJ");
		return false;
	}

	_clear_db_opera_param(suParam); // 清除查询信息列表

	//检查结果
	UINT nRecordNum=0;
	RcdSet.get_record_num(nRecordNum);
	if(0==nRecordNum)//没有找到对应文件
	{
		RcdSet.clear_result();
		memset(cLogMsg,0,500);
		sprintf(cLogMsg,"___GetGroupCfg():全站的ied中,cSgName为%s的配置不存在.",cSgName);
		RcdErrLogWithParentClass(cLogMsg,"TNXEcProAsdu10FJ");
		return false;
	}
	string strValue;
	RcdSet.get_field_value_row(1,1,strValue);
	nGroup = atoi(strValue.c_str());

	RcdSet.get_field_value_row(2,1,strValue);
	nEntry = atoi(strValue.c_str());

	RcdSet.clear_result();
	memset(cLogMsg,0,500);
	sprintf(cLogMsg,"___GetGroupCfg():获取cSgName：%s,nGroup：%d.nEntry：%d.",cSgName,nGroup,nEntry);
	RcdTrcLogWithParentClass(cLogMsg,"TNXEcProAsdu10FJ");
	return true;
}
/**
* @brief         根据NX硬压板预校回应结果及召唤命令生成规约结果列表
* @param[in]     PRO_FRAME_BODY_LIST & lCmd:规约命令
* @param[out]    PRO_FRAME_BODY_LIST & lResult :规约信息体列表
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu10FJ::_CvtHardCheckResultToPro(PRO_FRAME_BODY_LIST & lCmd,PRO_FRAME_BODY_LIST & lResult)
{
	// 转换NX消息为ASDU10Info结构
	ASDU10_INFO Asdu10Info;
	int nRet = __CvtNxSubFieldToGenList_Int(Asdu10Info.GroupToGenListMap);

	__CvtAsdu10InfoToProBody(nRet,Asdu10Info,lCmd,lResult);

	// 清理ASDU10结构
	ClearAsdu10Info(Asdu10Info);
	return 0;
}

/**
* @brief         根据NX硬压板执行回应结果及召唤命令生成规约结果列表
* @param[in]     PRO_FRAME_BODY_LIST & lCmd:规约命令
* @param[out]    PRO_FRAME_BODY_LIST & lResult :规约信息体列表
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu10FJ::_CvtHardExcResultToPro(PRO_FRAME_BODY_LIST & lCmd,PRO_FRAME_BODY_LIST & lResult)
{
	// 转换NX消息为ASDU10Info结构
	ASDU10_INFO Asdu10Info;
	int nRet = __CvtNxSubFieldToGenList_Int(Asdu10Info.GroupToGenListMap);

	__CvtAsdu10InfoToProBody(nRet,Asdu10Info,lCmd,lResult);

	// 清理ASDU10结构
	ClearAsdu10Info(Asdu10Info);
	return 0;
}

/**
* @brief         根据NX机器人巡视执行回应结果及召唤命令生成规约结果列表
* @param[in]     PRO_FRAME_BODY_LIST & lCmd:规约命令
* @param[out]    PRO_FRAME_BODY_LIST & lResult :规约信息体列表
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu10FJ::_CvtRobertCheckResultToPro(PRO_FRAME_BODY_LIST & lCmd,PRO_FRAME_BODY_LIST & lResult)
{
	// 转换NX消息为ASDU10Info结构
	ASDU10_INFO Asdu10Info;
	int nRet = __CvtNxSubFieldToGenList_Int_Rot(Asdu10Info.GroupToGenListMap);

	__CvtAsdu10InfoToProBody(nRet,Asdu10Info,lCmd,lResult);

	// 清理ASDU10结构
	ClearAsdu10Info(Asdu10Info);
	return 0;
}
/**
* @brief         根据NX61850读值回应结果及召唤命令生成规约结果列表
* @param[in]     PRO_FRAME_BODY_LIST & lCmd:规约命令
* @param[out]    PRO_FRAME_BODY_LIST & lResult :规约信息体列表
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu10FJ::_Cvt61850ReadResultToPro(PRO_FRAME_BODY_LIST & lCmd,PRO_FRAME_BODY_LIST & lResult)
{
	// 转换NX消息为ASDU10Info结构
	ASDU10_INFO Asdu10Info;
	int nRet = __CvtNxSubFieldToGenList_Int_61850Read(Asdu10Info.GroupToGenListMap);

	__CvtAsdu10InfoToProBody(nRet,Asdu10Info,lCmd,lResult);

	// 清理ASDU10结构
	ClearAsdu10Info(Asdu10Info);
	return 0;
}
/**
* @brief         根据ASDU10信息结构格式化ASDU10报文体
* @param[in]     ASDU10_INFO &Asdu10Info:asdu10信息结构
* @param[in]     PRO_FRAME_BODY * pCmdBody:命令信息指针(若为NULL,表明该帧转换自动上送信息)
* @param[out]    PRO_FRAME_BODY_LIST  & lBody :ASDU规约信息体列表
* @param[in]     int nReserve:备用
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu10FJ::FormatAsdu10Body(ASDU10_INFO &Asdu10Info,PRO_FRAME_BODY_LIST &lBody,PRO_FRAME_BODY * pCmdBody,int nReserve)
{
	char cError[255] = "";
	int nSrcSize = lBody.size();
	int nEntryNum = 0;  // 总条目数
	GROUP2GENLIST_MAP::iterator iteMap;
	ASDU_GEN_INFO_LIST * pGenList= NULL;

	if( NULL != pCmdBody )        // 根据命令生成结果
	{
		// 如果为控制执行命令，则根据命令构造结果
		if( ( pCmdBody->nType == 0x0A ) && (pCmdBody->nCot == 0x28) && 
			(pCmdBody->nFun == 0xfe) && (pCmdBody->nInf == 0xfa) 
			)
		{
			PRO_FRAME_BODY ProBody;
			ProBody = *pCmdBody; // 结果与命令一致
			lBody.push_back(ProBody);
			ProBody.vVarData.clear();
			return EC_PRO_CVT_SUCCESS;
		}

		// 如果为机器人执行命令或新疆对点读值，则根据命令构造结果
		if( ( ( pCmdBody->nType == 0x0A ) && (pCmdBody->nCot == 0x2A) && 
			(pCmdBody->nFun == 0xfe) && (pCmdBody->nInf == 0xf2) 
			) || ( ( pCmdBody->nType == 0x0A ) && (pCmdBody->nCot == 0x2A) && 
			(pCmdBody->nFun == 0xfe) && (pCmdBody->nInf == 0xfe)) )
		{
			PRO_FRAME_BODY ProBody;
			ProBody = *pCmdBody; // 结果与命令一致
			lBody.push_back(ProBody);
			ProBody.vVarData.clear();
			return EC_PRO_CVT_SUCCESS;
		}

		// 如果为召唤组标题命令，需特殊处理(因命令中没有组号信息)
		if( (pCmdBody->nType == 0x15 ) && ( pCmdBody->nFun == 0xfe ) && (pCmdBody->nInf == 0xf0 ) )
		{
			if( Asdu10Info.GroupToGenListMap.size() <= 0 )
			{
				sprintf(cError,"FormatAsdu10Body():asdu10结构中组标题结果为0条.(cmd:addr=%d cpu=%d),生成组标题失败回应",
					pCmdBody->nAddr,pCmdBody->nCpu);
				RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
				MakeGenericFailResultByCmd(pCmdBody,lBody); // 生成失败回应
				return EC_PRO_CVT_SUCCESS;
			}
			iteMap = Asdu10Info.GroupToGenListMap.begin();
			if( iteMap->second != NULL )
				nEntryNum = iteMap->second->size();
		}
		else  // 判断ASDU10信息结构中是否有对应命令中组号的结果
		{
			if( Asdu10Info.GroupToGenListMap.count(pCmdBody->nGroup) <= 0 )  // 没有对应组的结果
			{
				sprintf(cError,"FormatAsdu10Body():asdu10结构中没有组号=%d的结果与命令对应(cmd:addr=%d cpu=%d zone=%d group=%d),生成该组失败回应",
					pCmdBody->nGroup,pCmdBody->nAddr,pCmdBody->nCpu,pCmdBody->nZone,pCmdBody->nGroup);
				RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
				MakeGenericFailResultByCmd(pCmdBody,lBody); 	// 生成失败回应
				return EC_PRO_CVT_SUCCESS;
			}
			pGenList = Asdu10Info.GroupToGenListMap[pCmdBody->nGroup];
			if( pGenList != NULL )
				nEntryNum = pGenList->size();
		}

		// 设置ASDU10信息结构的基本信息
		Asdu10Info.Addr.nAddr = pCmdBody->nAddr;
		Asdu10Info.Addr.nSubstationAdd = pCmdBody->nSubstationAdd;
		Asdu10Info.Addr.nCpu = pCmdBody->nCpu;
		//Asdu10Info.Addr.nZone = pCmdBody->nZone; // 结果中定值区号回应可能与命令不一致(在具体应用中设置）
		Asdu10Info.InfoObj.nFun = pCmdBody->nFun;
		Asdu10Info.InfoObj.nInf = pCmdBody->nInf;

		// 如果为控制预校命令
		if( ( pCmdBody->nType == 0x0A )&&(pCmdBody->nCot == 0x28)&&(pCmdBody->nFun == 0xfe)&&(pCmdBody->nInf == 0xf9) )
		{
			Asdu10Info.nCot = 0x2c;     // 预校成功的确认传输原因
		}
		else
		{
			Asdu10Info.nCot = pCmdBody->nCot;
		}
		// 格式化该组报文体
		if( _FormatOneGroupAsdu10Body(Asdu10Info,pCmdBody->nGroup,pCmdBody->nRii,lBody) != EC_PRO_CVT_SUCCESS )
		{
			MakeGenericFailResultByCmd(pCmdBody,lBody); // 生成失败回应
			nEntryNum = 0;
		}
		sprintf(cError,"FormatAsdu10Body():为cmd(addr=%d cpu=%d inf=%d group=%d rii=%d)生成%d条asdu10,总条目数:%d",
			pCmdBody->nAddr,pCmdBody->nCpu,pCmdBody->nInf,pCmdBody->nGroup,pCmdBody->nRii,lBody.size()-nSrcSize,nEntryNum);
	}
	else     // 自动上送
	{
		// 格式化所有组自动上送
		iteMap = Asdu10Info.GroupToGenListMap.begin();
		while( iteMap != Asdu10Info.GroupToGenListMap.end() )
		{
			_FormatOneGroupAsdu10Body(Asdu10Info,iteMap->first,0,lBody);
			pGenList = iteMap->second;
			if( pGenList != NULL )
				nEntryNum += pGenList->size();
			++iteMap;
		}
		sprintf(cError,"FormatAsdu10Body():生成%d条自动上送asdu10,addr=%d cpu=%d 总条目数=%d ",
			lBody.size()-nSrcSize,Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu,nEntryNum);
	}
	RcdTrcLogWithParentClass(cError,"TNXEcProAsdu10FJ");
	return 0;
}
/**
* @brief         根据ASDU10信息命令生成失败回应
* @param[in]     PRO_FRAME_BODY * pCmdBody:命令信息
* @param[out]    PRO_FRAME_BODY_LIST & lResult :规约信息体列表
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu10FJ::MakeGenericFailResultByCmd(PRO_FRAME_BODY * pCmdBody,PRO_FRAME_BODY_LIST & lResult)
{
	// 根据命令生成失败回应(需判断命令为asdu21或控制类命令asdu10)
	if( pCmdBody == NULL )
		return EC_PRO_CVT_FAIL;

	// 先通过命令构造结构
	PRO_FRAME_BODY ProBody(*pCmdBody);

	// 修改 ASDU类型
	ProBody.nType = 0x0A;

	// 修改其它
	if( pCmdBody->nType == 0x0A) // asdu10 (控制命令)
	{
		if ((pCmdBody->nFun == 0xfe) && (pCmdBody->nInf == 0xf2))
		{
			// 修改传输原因
			ProBody.nCot = 0x2b;

			// 结束帧
			ProBody.bLast = true;
			// 如果不为召唤组标题，需增加通用分类无效回应数据
			if( pCmdBody->nInf != 0xf0 )
			{
				ProBody.vVarData.push_back(GDD_DATA_TYPE_GRC);  // 数据类型-通用分类回答码
				ProBody.vVarData.push_back(0x01);               // 数据宽度
				ProBody.vVarData.push_back(0x01);               // 数据数目
				ProBody.vVarData.push_back(0x02);               // GRC 2-不存在请求的数据
			}
		} 
		else
		{
			// 修改传输原因
			ProBody.nCot = 0x29;    // 否定认可
		}	
	}
	else
	{
		// 修改传输原因
		ProBody.nCot = 0x2b;

		// 结束帧
		ProBody.bLast = true;
		// 如果不为召唤组标题，需增加通用分类无效回应数据
		if( pCmdBody->nInf != 0xf0 )
		{
			ProBody.vVarData.push_back(GDD_DATA_TYPE_GRC);  // 数据类型-通用分类回答码
			ProBody.vVarData.push_back(0x01);               // 数据宽度
			ProBody.vVarData.push_back(0x01);               // 数据数目
			ProBody.vVarData.push_back(0x02);               // GRC 2-不存在请求的数据
		}
	}

	lResult.push_back(ProBody);

	// 清空规约体
	ProBody.vVarData.clear();
	return 0;
}
/**
* @brief         根据NX事件信息生成规约事件列表
* @param[in]     NX_EVENT_MESSAGE * pMsg :事件信结构指针
* @param[out]    PRO_FRAME_BODY_LIST  & lBody :规约信息体
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu10FJ::ConvertEventMsgToPro(NX_EVENT_MESSAGE* pMsg,PRO_FRAME_BODY_LIST & lBody)
{
	char cError[255] = "";
	int  nRet = EC_PRO_CVT_FAIL ;

	m_pEventMsg = pMsg;

	switch(pMsg->n_msg_type)
	{
	case NX_IED_EVENT_SOFTTRAP_REPORT:         // 软压板
		nRet = _CvtSoftReportToPro(lBody);
		break;
	case NX_SYS_EVENT_IED_SGZONE_CHG_REPORT:   // 定值区变化
		nRet = _CvtZoneChgReportToPro(lBody);
		break;
	case NX_IED_CALL_ROBOTCHECK_REPORT:   // 机器人巡视报告
		nRet = _CvtRobertCheckReportToPro(lBody);
		break;
	default:
		sprintf(cError,"ConvertEventMsgToPro()中暂不支持n_msg_type=%d的消息处理",pMsg->n_msg_type);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
		nRet = EC_PRO_CVT_NOSUPPORT;
		break;
	}
	m_pEventMsg = NULL;
	return nRet;
}
/**
* @brief         转换规约信息到NX通用消息结构
* @param[in]     PRO_FRAME_BODY_LIST* pBodyList :规约信息体列表指针
* @param[out]    NX_COMMON_MSG_LIST & lMsg: 保存生成的通用消息列表
* @param[out]    PRO_FRAME_BODY_LIST & lResult：保存生成的规约失败回应(服务端规约有效）
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu10FJ::ConvertProToCommonMsg(PRO_FRAME_BODY_LIST* pBodyList,NX_COMMON_MSG_LIST & lMsg,PRO_FRAME_BODY_LIST & lResult)
{
	char cError[255] = "";
	int  nRet = EC_PRO_CVT_FAIL ;            // 返回值
	int  nGropTitleType = 0;                 //组标题类型
	ASDU10_INFO  Asdu10Info;

	if( pBodyList == NULL )
		return nRet;
	string striii;

	// 将规约报文转换为ASDU10信息结构
	if( ProBodyToAsdu10Struct(*pBodyList,Asdu10Info) != EC_PRO_CVT_SUCCESS )
	{
		_MakeRCtlFailResultByCmd(*pBodyList,lResult);
		ClearAsdu10Info(Asdu10Info);
		return nRet;
	}
	// 	确定通用分类组至少一组
	// 		if( Asdu10Info.GroupToGenListMap.size() <= 0 )
	// 		{
	// 			sprintf(cError,"收到ASDU10(addr=%d fun=%d inf=%d cot =%d)没有组信息,处理失败",
	// 				    Asdu10Info.Addr.nAddr,Asdu10Info.InfoObj.nFun,Asdu10Info.InfoObj.nInf,Asdu10Info.nCot);
	// 			RcdErrLogWithParentClass(cError,"TNXEcProAsdu10");
	// 			_MakeRCtlFailResultByCmd(*pBodyList,lResult);
	// 			ClearAsdu10Info(Asdu10Info);
	// 			return nRet;
	// 		}
	// 		// 根据通用分类组号获得组类型(取头一组判断即可，同一次命令中的多组都为同一类型)
	// 		GROUP2GENLIST_MAP::iterator ite = Asdu10Info.GroupToGenListMap.begin();
	nGropTitleType = _GetGroupTitleType(Asdu10Info.Addr.nSubstationAdd,Asdu10Info.Addr.nAddr,
		Asdu10Info.Addr.nCpu,Asdu10Info.nGroup);

	// 根据通用分类信息区分命令类型
	if( ( Asdu10Info.nCot == 0x28) && (Asdu10Info.InfoObj.nFun == 0xfe) )  // 控制命令
	{
		if( Asdu10Info.InfoObj.nInf == 0xf9 ) // 预校
		{
			nRet = _CvtAsdu10InfoToRCtlCheck(nGropTitleType,Asdu10Info,lMsg);
		}
		else if ( Asdu10Info.InfoObj.nInf == 0xfa) // 执行
		{
			nRet = _CvtAsdu10InfoToRCtlExc(nGropTitleType,Asdu10Info,lMsg);
		}
		else
		{
			//错误
			sprintf(cError,"ConvertProToCommonMsg()中收到的控制命令nInf=%d,错误不处理",Asdu10Info.InfoObj.nInf);
			RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
		}
	}
	else if (( Asdu10Info.nCot == 0x2a) && (Asdu10Info.InfoObj.nFun == 0xfe) && (Asdu10Info.InfoObj.nInf == 0xf2))//机器人巡视命令 update by yys
	{
		nRet = _CvtRobotProToNX(Asdu10Info,lMsg);
	}
	else
	{
		//其它命令暂不支持处理
		sprintf(cError,"ConvertProToCommonMsg()中不支持cot=%d、Fun=%d、Inf=%d的消息处理",
			Asdu10Info.nCot,Asdu10Info.InfoObj.nFun,Asdu10Info.InfoObj.nInf);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
		nRet = EC_PRO_CVT_NOSUPPORT;
	}

	// 转换失败或不支持的处理
	if( nRet != EC_PRO_CVT_SUCCESS ) 
	{
		_MakeRCtlFailResultByCmd(*pBodyList,lResult);
	}

	// 清理ASDU10结构
	ClearAsdu10Info(Asdu10Info);

	return nRet;
}
/**
* @brief         根据ASDU10信息转换为NX软压板投退预校命令
* @param[in]     ASDU10_INFO &Asdu10Info:asdu10信息结构
* @param[out]    NX_COMMON_MSG_LIST& lMsg:通用消息列表
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu10FJ::_CvtRobotProToNX(ASDU10_INFO & Asdu10Info,NX_COMMON_MSG_LIST& lMsg)
{
	char cError[255]="";
	NX_COMMON_MESSAGE CommonMsg;

	int nRet = __CvtRotInfoToCommonMsg(Asdu10Info,CommonMsg);
	if( nRet != EC_PRO_CVT_SUCCESS )
		return nRet;

	// 判断转换后生成的NX子集是否大于0
	if( CommonMsg.list_subfields.size() <= 0 )
	{
		sprintf(cError,"_CvtRobotProToNX():机器人巡视命令(addr=%d cpu=%d)时,没有生成对应的巡视点信息",
			Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
		return EC_PRO_CVT_FAIL;
	}

	// 将生成的通用消息添加到通用消息列表
	lMsg.push_back(CommonMsg);

	sprintf(cError,"_CvtRobotProToNX():机器人巡视命令(addr=%d cpu=%d)转换为通用消息成功,生成%d个巡视点信息",
		Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu,CommonMsg.list_subfields.size());
	RcdTrcLogWithParentClass(cError,"TNXEcProAsdu10FJ");

	m_CsgLogDataUnit.e_log_unit_datatype = CSG_LOG_UNIT_DATATYPE_OTHER;
	m_CsgLogDataUnit.e_attribute         = CSG_LOG_UNIT_ATTRIBUTE_READ;
	m_CsgLogDataUnit.str_sponer    = "主站";
	m_CsgLogDataUnit.e_behavior  = CSG_LOG_UNIT_BEHAVIOR_DATA_RCV_ANALYZE;
	m_CsgLogDataUnit.str_target    = "子站";
	m_CsgLogDataUnit.str_dataobject = "机器人巡视命令.";
	_ZERO_MEM(cError,255);
	sprintf(cError,"addr=%d cpu=%d",Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu);
	m_CsgLogDataUnit.str_dataauxiliary = cError;
	CsgLogOutOpr(m_CsgLogDataUnit);

	CommonMsg.list_subfields.clear();
	return EC_PRO_CVT_SUCCESS;
}
/**
* @brief         根据ASDU10信息转换为NX61850读值服务
* @param[in]     ASDU10_INFO &Asdu10Info:asdu10信息结构
* @param[out]    NX_COMMON_MSG_LIST& lMsg:通用消息列表
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu10FJ::_CvtProToNX61850Read(ASDU10_INFO & Asdu10Info,NX_COMMON_MSG_LIST& lMsg)
{
	char cError[255]="";
	NX_COMMON_MESSAGE CommonMsg;

	int nRet = __CvtReadPointInfoToCommonMsg(Asdu10Info,CommonMsg);
	if( nRet != EC_PRO_CVT_SUCCESS )
		return nRet;

	// 判断转换后生成的NX子集是否大于0
	if( CommonMsg.list_subfields.size() <= 0 )
	{
		sprintf(cError,"_CvtProToNX61850Read():机器人巡视命令(addr=%d cpu=%d)时,没有生成对应的巡视点信息",
			Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
		return EC_PRO_CVT_FAIL;
	}

	// 将生成的通用消息添加到通用消息列表
	lMsg.push_back(CommonMsg);

	sprintf(cError,"_CvtProToNX61850Read():61850单点读值(addr=%d cpu=%d)转换为通用消息成功.",
		Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu);
	RcdTrcLogWithParentClass(cError,"TNXEcProAsdu10FJ");

	m_CsgLogDataUnit.e_log_unit_datatype = CSG_LOG_UNIT_DATATYPE_OTHER;
	m_CsgLogDataUnit.e_attribute         = CSG_LOG_UNIT_ATTRIBUTE_READ;
	m_CsgLogDataUnit.str_sponer    = "主站";
	m_CsgLogDataUnit.e_behavior  = CSG_LOG_UNIT_BEHAVIOR_DATA_RCV_ANALYZE;
	m_CsgLogDataUnit.str_target    = "子站";
	m_CsgLogDataUnit.str_dataobject = "61850单点读值";
	_ZERO_MEM(cError,255);
	sprintf(cError,"addr=%d cpu=%d",Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu);
	m_CsgLogDataUnit.str_dataauxiliary = cError;
	CsgLogOutOpr(m_CsgLogDataUnit);

	CommonMsg.list_subfields.clear();
	return EC_PRO_CVT_SUCCESS;
}
/**
* @brief         转换为NX机器人巡视命令
* @param[in]     ASDU10_INFO &Asdu10Info:asdu10信息结构
* @param[out]    NX_COMMON_MSG_LIST& lMsg:通用消息列表
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu10FJ::__CvtRotInfoToCommonMsg(ASDU10_INFO & Asdu10Info,NX_COMMON_MESSAGE &CommonMsg)
{
	char cError[255]="";
	// 获得地址信息
	const IED_TB * pIedTb = m_pModelSeek->GetIedBasicCfgByAddr103(Asdu10Info.Addr.nAddr);
	if( pIedTb == NULL )
	{
		return EC_PRO_CVT_FAIL;
	}

	CommonMsg.n_msg_topic = NX_TOPIC_COMMAND;
	CommonMsg.n_msg_type  = NX_IED_CALL_ROBOTCHECK_ASK;
	CommonMsg.n_obj_id    = pIedTb->n_obj_id;
	CommonMsg.n_obj_type  = NX_OBJ_TYPE_NX_IED;
	CommonMsg.n_sub_obj_id= Asdu10Info.Addr.nCpu;
	CommonMsg.n_sub_sub_obj_id = Asdu10Info.Addr.nZone;
	CommonMsg.n_data_src = 2;  // 表示命令来自远方主站
	CommonMsg.b_lastmsg  = true;

	// 转换NX_SUBFIELDS
	NX_COMMON_MSG_SUBFILED_LIST * pNxSubList = &CommonMsg.list_subfields;
	GROUP2GENLIST_MAP::iterator iteMap = Asdu10Info.GroupToGenListMap.begin();
	ASDU_GEN_INFO_LIST * pGenList = NULL;
	ASDU_GEN_INFO_LIST::iterator ite;
	NX_COMMON_FIELD_STRUCT tmpSubField;
	string strValue;
	int nID = 0;

	while( iteMap != Asdu10Info.GroupToGenListMap.end() )
	{
		pGenList = iteMap->second;
		if( pGenList == NULL )
		{
			++iteMap;
			continue;
		}
		// 遍历通用分类数据集列表
		ite = pGenList->begin();
		while (ite != pGenList->end())
		{
			tmpSubField.n_field_id = -1;
			_ZERO_MEM(tmpSubField.c_value,sizeof(tmpSubField.c_value));
			tmpSubField.n_value = -1;
			strValue.clear();
			nID = 0;

			// 将通用分类数据类型转换为字符值
			strValue = ___CvtGenericValueToStr(*ite);
			printf("--------------------------------->%s\n",strValue.c_str());

			memset(tmpSubField.c_field_name,0,strlen(strValue.c_str()));
			memcpy(tmpSubField.c_field_name,strValue.c_str(),strlen(strValue.c_str()));
			//memcpy(tmpSubField.c_field_name,strValue.c_str(),_MIN_VAL(strValue.size(),sizeof(tmpSubField.c_value)-1));

			sprintf(cError,"_CvtProToNX61850Read():处理后下发的巡视设备名称为：%s.",
				tmpSubField.c_field_name);
			RcdTrcLogWithParentClass(cError,"TNXEcProAsdu10FJ");
			// 加入NX子集列表
			pNxSubList->push_back(tmpSubField);

			++ite;
		} 

		pGenList = NULL;
		++iteMap;
	} 

	return EC_PRO_CVT_SUCCESS;
}
/**
* @brief         转换为NX61850渎职
* @param[in]     ASDU10_INFO &Asdu10Info:asdu10信息结构
* @param[out]    NX_COMMON_MSG_LIST& lMsg:通用消息列表
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu10FJ::__CvtReadPointInfoToCommonMsg(ASDU10_INFO & Asdu10Info,NX_COMMON_MESSAGE &CommonMsg)
{
	// 获得地址信息
	char cError[255]="";
	const IED_TB * pIedTb = m_pModelSeek->GetIedBasicCfgByAddr103(Asdu10Info.Addr.nAddr);
	if( pIedTb == NULL )
	{
		return EC_PRO_CVT_FAIL;
	}

	CommonMsg.n_msg_topic = NX_TOPIC_COMMAND;
	CommonMsg.n_msg_type  = NX_IED_CALL_61850SRV_READ_ASK;
	CommonMsg.n_obj_id    = pIedTb->n_obj_id;
	CommonMsg.n_obj_type  = NX_OBJ_TYPE_NX_IED;
	CommonMsg.n_sub_obj_id= Asdu10Info.Addr.nCpu;
	CommonMsg.n_sub_sub_obj_id = Asdu10Info.Addr.nZone;
	sprintf(CommonMsg.c_obj_pathname,"%s",pIedTb->str_objpathname.c_str());
	CommonMsg.n_data_src = 2;  // 表示命令来自远方主站
	CommonMsg.b_lastmsg  = true;

	// 转换NX_SUBFIELDS
	NX_COMMON_MSG_SUBFILED_LIST * pNxSubList = &CommonMsg.list_subfields;
	GROUP2GENLIST_MAP::iterator iteMap = Asdu10Info.GroupToGenListMap.begin();
	ASDU_GEN_INFO_LIST * pGenList = NULL;
	ASDU_GEN_INFO_LIST::iterator ite;
	NX_COMMON_FIELD_STRUCT tmpSubField;
	string strValue;
	int nID = 0;

	while( iteMap != Asdu10Info.GroupToGenListMap.end() )
	{
		pGenList = iteMap->second;
		if( pGenList == NULL )
		{
			++iteMap;
			continue;
		}
		// 遍历通用分类数据集列表
		ite = pGenList->begin();
		while (ite != pGenList->end())
		{
			tmpSubField.n_field_id = -1;
			_ZERO_MEM(tmpSubField.c_value,sizeof(tmpSubField.c_value));
			tmpSubField.n_value = -1;
			strValue.clear();

			const SG_TB* pSgCfg = m_pModelSeek->GetIedSgCfg(pIedTb->n_obj_id,Asdu10Info.Addr.nCpu,ite->nGroup,ite->nEntry);
			if (pSgCfg == NULL)
			{
				sprintf(cError,"__CvtReadPointInfoToCommonMsg():[GetIedSgCfg]获取定值配置失败，跳过");
				RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
				continue;
			}

			sprintf(tmpSubField.c_field_name,"%s",pSgCfg->str_objpathname.c_str());

			// 加入NX子集列表
			pNxSubList->push_back(tmpSubField);

			++ite;
		} 

		pGenList = NULL;
		++iteMap;
	} 

	return EC_PRO_CVT_SUCCESS;
}
/**
* @brief         转换NX通用消息子集数据（值为整型）为组号与通用分类数据集映射表
* @param[out]     GROUP2GENLIST_MAP & GToGenListMap:通用分类数据集映射表
* @return        int 0-成功 其它失败
*/
//int TNXEcProAsdu10FJ::__CvtNxSubFieldToGenList_Int(GROUP2GENLIST_MAP & GToGenListMap)
//{
//	char cError[255]="";
//	ASDU_GEN_INFO GenData;     // 通用分类数据结构
//	ASDU_GEN_INFO_LIST * pGenList = NULL;
//	u_int8 nGroup = 0,nEntry = 0,nDataType=0,nKod=0;
//	bool  bGetCfg = false,bDpi = false;
//	int nValue = 0;
//
//	// 判断回应的总结果
//	if(m_pCommonMsg->n_result != 0 )   // 失败
//		return EC_PRO_CVT_FAIL;
//
//	// 转换NX事件子集结构为通用分类数据
//	NX_COMMON_MSG_SUBFILED_LIST::iterator ite = m_pCommonMsg->list_subfields.begin();
//	while ( ite != m_pCommonMsg->list_subfields.end() )
//	{
//		GenData.vGid.clear();
//		// 获得组号\条目号及数据类型信息
//		if( ( m_pCommonMsg->n_msg_type == NX_IED_CALL_SGZONE_REP) || 
//			( m_pCommonMsg->n_msg_type == NX_IED_CTRL_SGZONE_CHECK_REP) ||
//			( m_pCommonMsg->n_msg_type == NX_IED_CTRL_SGZONE_EXC_REP) 
//			)
//		{
//			bGetCfg = ___GetZoneDataPoint103Cfg(ite->n_field_id,nGroup,nEntry,nDataType);
//			nValue = ite->n_value;
//		}
//		else if( ( m_pCommonMsg->n_msg_type == NX_IED_CALL_SOFTSTRAP_REP) ||
//			( m_pCommonMsg->n_msg_type == NX_IED_CTRL_SOFTSTRAP_CHECK_REP) || 
//			( m_pCommonMsg->n_msg_type == NX_IED_CTRL_SOFTSTRAP_EXC_REP)
//			)
//		{
//			bDpi = true;
//			bGetCfg = ___GetSoftDataPoint103Cfg(ite->n_field_id,nGroup,nEntry,nDataType);
//			if( ite->n_value == 1 )
//				nValue = 2;
//			else
//				nValue = 1;
//		}
//		else if( ( m_pCommonMsg->n_msg_type == NX_IED_CALL_HARDSTRAP_REP) ||
//			( m_pCommonMsg->n_msg_type == NX_IED_CTRL_HARDSTRAP_CHECK_REP) || 
//			( m_pCommonMsg->n_msg_type == NX_IED_CTRL_HARDSTRAP_EXC_REP)
//			)
//		{
//			bDpi = true;
//			bGetCfg = ___GetHardDataPoint103Cfg(ite->n_field_id,nGroup,nEntry,nDataType);
//			if( ite->n_value == 1 )
//				nValue = 2;
//			else
//				nValue = 1;
//		}
//		else
//		{
//			sprintf(cError,"__CvtNxSubFieldToGenList_Int():不支持获取n_msg_type=%d消息的103点配置",
//				m_pCommonMsg->n_msg_type);
//			RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
//		}
//
//		if( !bGetCfg)
//		{
//			++ite;
//			continue;
//		}
//
//		nKod = ___GetGenKod(m_pCommonMsg->n_data_src);
//
//		__SetGenericIntData(nGroup,nEntry,nKod,nValue,GenData,bDpi);
//
//		// 将通用分类数据加入列表
//		if( GToGenListMap.count(nGroup) > 0 )    // 该组已存在则获取对应的列表指针
//		{
//			pGenList = GToGenListMap[nGroup];
//		}
//		else      // 不存在需新建
//		{
//			pGenList = new ASDU_GEN_INFO_LIST;
//			GToGenListMap[nGroup] = pGenList;
//		}
//
//		if( pGenList != NULL )
//			pGenList->push_back(GenData);
//
//		++ite;
//		pGenList = NULL;
//		nGroup = nEntry = nKod = nDataType = 0;
//		bDpi  = false;
//		bGetCfg = false;
//		nValue = 0;
//	}
//
//	GenData.vGid.clear();
//	return EC_PRO_CVT_SUCCESS;
//}
/**
* @brief         转换NX通用消息子集数据（值为整型）为组号与通用分类数据集映射表
* @param[out]     GROUP2GENLIST_MAP & GToGenListMap:通用分类数据集映射表
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu10FJ::__CvtNxSubFieldToGenList_Int_Rot(GROUP2GENLIST_MAP & GToGenListMap)
{
	char cError[255]="";
	ASDU_GEN_INFO GenData;     // 通用分类数据结构
	ASDU_GEN_INFO_LIST * pGenList = NULL;
	u_int8 nGroup = 0,nEntry = 0,nDataType=0,nKod=0;
	bool  bGetCfg = false,bDpi = false;
	int nValue = 0;

	// 判断回应的总结果
// 	if(m_pCommonMsg->n_result != 0 )   // 失败
// 		return EC_PRO_CVT_FAIL;


	GenData.vGid.clear();
	// 获得组号\条目号及数据类型信息
	if(  m_pCommonMsg->n_msg_type == NX_IED_CALL_ROBOTCHECK_REP) 		
	{
		bGetCfg = __DBQuerySgGroup(m_pCommonMsg->n_obj_id,nGroup,nEntry);
		nValue = 0;
	}
	else
	{
		sprintf(cError,"__CvtNxSubFieldToGenList_Int():不支持获取n_msg_type=%d消息的103点配置",
			m_pCommonMsg->n_msg_type);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
	}


	nKod = 1;

	__SetGenericIntData(nGroup,nEntry,nKod,nValue,GenData,bDpi);

	// 将通用分类数据加入列表
	if( GToGenListMap.count(nGroup) > 0 )    // 该组已存在则获取对应的列表指针
	{
		pGenList = GToGenListMap[nGroup];
	}
	else      // 不存在需新建
	{
		pGenList = new ASDU_GEN_INFO_LIST;
		GToGenListMap[nGroup] = pGenList;
	}

	if ( m_pCommonMsg->n_result == 0 )//成功
	{
		GenData.nKod = 1;
	} 
	else
	{
		GenData.nKod = 0;
	}

	if( pGenList != NULL )
		pGenList->push_back(GenData);


	pGenList = NULL;
	nGroup = nEntry = nKod = nDataType = 0;
	bDpi  = false;
	bGetCfg = false;
	nValue = 0;


	GenData.vGid.clear();
	return EC_PRO_CVT_SUCCESS;
}
/**
* @brief         61850读值转换NX通用消息子集数据（值为整型）为组号与通用分类数据集映射表
* @param[out]     GROUP2GENLIST_MAP & GToGenListMap:通用分类数据集映射表
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu10FJ::__CvtNxSubFieldToGenList_Int_61850Read(GROUP2GENLIST_MAP & GToGenListMap)
{
	char cError[255]="";
	ASDU_GEN_INFO GenData;     // 通用分类数据结构
	ASDU_GEN_INFO_LIST * pGenList = NULL;
	u_int8 nGroup = 0,nEntry = 0,nDataType=0,nKod=0;
	bool  bGetCfg = false,bDpi = false;
	int nValue = 0;

	GenData.vGid.clear();

	__SetGenericIntData(nGroup,nEntry,nKod,nValue,GenData,bDpi);

	// 将通用分类数据加入列表
	if( GToGenListMap.count(nGroup) > 0 )    // 该组已存在则获取对应的列表指针
	{
		pGenList = GToGenListMap[nGroup];
	}
	else      // 不存在需新建
	{
		pGenList = new ASDU_GEN_INFO_LIST;
		GToGenListMap[nGroup] = pGenList;
	}

	if( pGenList != NULL )
		pGenList->push_back(GenData);


	pGenList = NULL;
	nGroup = nEntry = nKod = nDataType = 0;
	bDpi  = false;
	bGetCfg = false;
	nValue = 0;


	GenData.vGid.clear();
	return EC_PRO_CVT_SUCCESS;
}
/**
* @brief         获得硬压板信息点的103配置
* @param[in]     nGroup:信息点编号
* @param[out]    nGroup:组号
* @param[out]    nItem: 条目号
* @param[out]    nDataType:数据类型
* @return        bool:true-成功 false-失败
*/
bool TNXEcProAsdu10FJ::___GetHardDataPoint103Cfg(int nID,u_int8 &nGroup,u_int8 &nEntry,u_int8 &nDataType)
{
	char cError[255] = "";
	int nReal;
	if (0 !=__DBQueryRealIed(m_pCommonMsg->n_obj_id,m_pCommonMsg->n_sub_obj_id,nReal))
	{
		sprintf(cError,"___GetHardDataPoint103Cfg():获取设备[IED=%d,CPU=%d] 的真实IED失败.",m_pCommonMsg->n_obj_id,m_pCommonMsg->n_sub_obj_id);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
		return false;
	}

	int nRealId = nReal/1000;
	int nRealCpu= nReal%1000;

	const STRAP_TB * pTb = NULL;
	pTb = m_pModelSeek->GetIedDiCfg(nRealId,nRealCpu,nID);
	if( pTb == NULL )
	{
		sprintf(cError,"___GetHardDataPoint103Cfg():获取IedId=%d,LD=%d,ID=%d的配置失败",
			m_pCommonMsg->n_obj_id,m_pCommonMsg->n_sub_obj_id,nID);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
		return false;
	}
	nGroup = pTb->n_outgroup103;
	nEntry = pTb->n_outitem103;
	nDataType = VALUE_INT;
	return true;
}
/*****************************************************************
* @brief         从数据库中查找指定厂站ID.
* @param[in]     int nIed_Id:设备ID.
* @param[out]    INT nStation_Id: 厂站ID.
* @return        int 0-成功 其它失败
****************************************************************/
int TNXEcProAsdu10FJ::__DBQueryRealIed(int nIed_Id,int nld_code,int & nReal)
{
	INXEcModelMgr * pModelMgr;
	DB_OPER_PARAM suParam;				 // 数据库查询参数结构体
	DB_FIELD_DATA suField;               //字段结构体
	DB_CONDITION  suCondition;           //条件结构体
	CPlmRecordSet RcdSet;				 //结果数据集

	char cDBError[255]="";
	char cLogMsg[500]="";

	//要查询出的连个字段
	suField.str_fd_name="strbackup1";			//真实ID
	suParam.lst_fddata.push_back(suField);


	//查询表名
	suParam.lst_tablename.push_back("nx_t_ied_hardstrap_cfg");

	//查询条件1  设备ID
	suCondition.str_cdt_name="obj_id";
	char cIed_Id[50]="";
	sprintf(cIed_Id,"%d",nIed_Id);
	suCondition.str_cdt_value=cIed_Id;
	suCondition.e_cdt_type		=CDT_TYPE_EQUAL;
	suCondition.e_cdt_val_type  =FD_TYPE_NUMERIC;
	suParam.lst_condition.push_back(suCondition);

	//查询条件1  设备ID
	suCondition.str_cdt_name="ld_code";
	sprintf(cIed_Id,"%d",nld_code);
	suCondition.str_cdt_value=cIed_Id;
	suCondition.e_cdt_type		=CDT_TYPE_EQUAL;
	suCondition.e_cdt_val_type  =FD_TYPE_NUMERIC;
	suParam.lst_condition.push_back(suCondition);

	//查询nx_t_ied_comtrade_list历史录波列表中,是否存在符合指定条件的记录.
	pModelMgr=m_pModelSeek->GetModelMgrObj();	
	int nRet=pModelMgr->dbm_select_records(&suParam,RcdSet,false,cDBError);
	if(nRet!=0)
	{
		_clear_db_opera_param(suParam); // 清除查询信息列表
		sprintf(cLogMsg,"__DBQueryRealIed():全站查询ied为%d，ld_code为%d的信息时出错[%s]",nIed_Id,nld_code,cDBError);
		RcdErrLogWithParentClass(cLogMsg,"TNXEcProAsdu21FJ");
		return EC_PRO_CVT_FAIL;
	}

	_clear_db_opera_param(suParam); // 清除查询信息列表

	//检查结果
	UINT nRecordNum=0;
	RcdSet.get_record_num(nRecordNum);
	if(0==nRecordNum)//没有找到对应文件
	{
		RcdSet.clear_result();
		memset(cLogMsg,0,500);
		sprintf(cLogMsg,"__DBQueryRealIed():全站的ied中,ied为%d,ld_code为%d的配置不存在.",nIed_Id,nld_code);
		RcdErrLogWithParentClass(cLogMsg,"TNXEcProAsdu21FJ");
		return EC_PRO_CVT_FAIL;
	}
	string strValue;
	RcdSet.get_field_value_row(1,1,strValue);
	nReal = atoi(strValue.c_str());

	RcdSet.clear_result();
	memset(cLogMsg,0,500);
	sprintf(cLogMsg,"__DBQueryRealIed():获取IED：%d,真实ID：%d.",nIed_Id,nReal);
	RcdTrcLogWithParentClass(cLogMsg,"TNXEcProAsdu21FJ");
	return EC_PRO_CVT_SUCCESS;
}

/*****************************************************************
* @brief         从数据库中查找指定厂站ID.
* @param[in]     int nIed_Id:设备ID.
* @param[out]    INT nStation_Id: 厂站ID.
* @return        int 0-成功 其它失败
****************************************************************/
int TNXEcProAsdu10FJ::__DBQuerySgGroup(int nIed_Id,u_int8 &nGroup,u_int8 &nEntry)
{
	INXEcModelMgr * pModelMgr;
	DB_OPER_PARAM suParam;				 // 数据库查询参数结构体
	DB_FIELD_DATA suField;               //字段结构体
	DB_CONDITION  suCondition;           //条件结构体
	CPlmRecordSet RcdSet;				 //结果数据集

	char cDBError[255]="";
	char cLogMsg[500]="";

	//要查询出的连个字段
	suField.str_fd_name="outgroup103";			//真实ID
	suParam.lst_fddata.push_back(suField);

	suField.str_fd_name="outitem103";			//真实ID
	suParam.lst_fddata.push_back(suField);

	//查询表名
	suParam.lst_tablename.push_back("nx_t_ied_sg_cfg");

	//查询条件1  设备ID
	suCondition.str_cdt_name="ied_obj";
	char cIed_Id[50]="";
	sprintf(cIed_Id,"%d",nIed_Id);
	suCondition.str_cdt_value=cIed_Id;
	suCondition.e_cdt_type		=CDT_TYPE_EQUAL;
	suCondition.e_cdt_val_type  =FD_TYPE_NUMERIC;
	suParam.lst_condition.push_back(suCondition);

// 	//查询条件1  设备ID
// 	suCondition.str_cdt_name="ld_code";
// 	sprintf(cIed_Id,"%d",nld_code);
// 	suCondition.str_cdt_value=cIed_Id;
// 	suCondition.e_cdt_type		=CDT_TYPE_EQUAL;
// 	suCondition.e_cdt_val_type  =FD_TYPE_NUMERIC;
// 	suParam.lst_condition.push_back(suCondition);

	//查询nx_t_ied_comtrade_list历史录波列表中,是否存在符合指定条件的记录.
	pModelMgr=m_pModelSeek->GetModelMgrObj();	
	int nRet=pModelMgr->dbm_select_records(&suParam,RcdSet,false,cDBError);
	if(nRet!=0)
	{
		_clear_db_opera_param(suParam); // 清除查询信息列表
		sprintf(cLogMsg,"__DBQueryRealIed():全站查询ied为%d，的信息时出错[%s]",nIed_Id,cDBError);
		RcdErrLogWithParentClass(cLogMsg,"TNXEcProAsdu10FJ");
		return EC_PRO_CVT_FAIL;
	}

	_clear_db_opera_param(suParam); // 清除查询信息列表

	//检查结果
	UINT nRecordNum=0;
	RcdSet.get_record_num(nRecordNum);
	if(0==nRecordNum)//没有找到对应文件
	{
		RcdSet.clear_result();
		memset(cLogMsg,0,500);
		sprintf(cLogMsg,"__DBQueryRealIed():全站的ied中,ied为%d的配置不存在.",nIed_Id);
		RcdErrLogWithParentClass(cLogMsg,"TNXEcProAsdu10FJ");
		return EC_PRO_CVT_FAIL;
	}
	string strValue;
	RcdSet.get_field_value_row(1,1,strValue);
	nGroup = atoi(strValue.c_str());

	RcdSet.get_field_value_row(2,1,strValue);
	nEntry = atoi(strValue.c_str());

	RcdSet.clear_result();
	memset(cLogMsg,0,500);
	sprintf(cLogMsg,"__DBQueryRealIed():获取IED：%d,组号：%d.条目号：%d.",nIed_Id,nGroup,nEntry);
	RcdTrcLogWithParentClass(cLogMsg,"TNXEcProAsdu10FJ");
	return EC_PRO_CVT_SUCCESS;
}

/**
* @brief         转换NX通用消息子集数据（值为整型）为组号与通用分类数据集映射表
* @param[out]     GROUP2GENLIST_MAP & GToGenListMap:通用分类数据集映射表
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu10FJ::__CvtNxSubFieldToGenList_Int(GROUP2GENLIST_MAP & GToGenListMap)
{
	char cError[255]="";
	ASDU_GEN_INFO GenData;     // 通用分类数据结构
	ASDU_GEN_INFO_LIST * pGenList = NULL;
	u_int8 nGroup = 0,nEntry = 0,nDataType=0,nKod=0;
	bool  bGetCfg = false,bDpi = false;
	int nValue = 0;

	// 判断回应的总结果
	if(m_pCommonMsg->n_result != 0 )   // 失败
		return EC_PRO_CVT_FAIL;

	// 转换NX事件子集结构为通用分类数据
	NX_COMMON_MSG_SUBFILED_LIST::iterator ite = m_pCommonMsg->list_subfields.begin();
	while ( ite != m_pCommonMsg->list_subfields.end() )
	{
		GenData.vGid.clear();
		// 获得组号\条目号及数据类型信息
		if( ( m_pCommonMsg->n_msg_type == NX_IED_CALL_SGZONE_REP) || 
			( m_pCommonMsg->n_msg_type == NX_IED_CTRL_SGZONE_CHECK_REP) ||
			( m_pCommonMsg->n_msg_type == NX_IED_CTRL_SGZONE_EXC_REP) 
			)
		{
			bGetCfg = ___GetZoneDataPoint103Cfg(ite->n_field_id,nGroup,nEntry,nDataType);
			nValue = ite->n_value;
		}
		else if( ( m_pCommonMsg->n_msg_type == NX_IED_CALL_SOFTSTRAP_REP) ||
			( m_pCommonMsg->n_msg_type == NX_IED_CTRL_SOFTSTRAP_CHECK_REP) || 
			( m_pCommonMsg->n_msg_type == NX_IED_CTRL_SOFTSTRAP_EXC_REP)
			)
		{
			bDpi = true;
			bGetCfg = ___GetSoftDataPoint103Cfg(ite->n_field_id,nGroup,nEntry,nDataType);
			if( ite->n_value == 1 )
				nValue = 2;
			else
				nValue = 1;
		}
		else if( ( m_pCommonMsg->n_msg_type == NX_IED_CALL_HARDSTRAP_REP) ||
			( m_pCommonMsg->n_msg_type == NX_IED_CTRL_HARDSTRAP_CHECK_REP) || 
			( m_pCommonMsg->n_msg_type == NX_IED_CTRL_HARDSTRAP_EXC_REP)
			)
		{
			bDpi = true;
			bGetCfg = ___GetHardDataPoint103Cfg(ite->n_field_id,nGroup,nEntry,nDataType);
			if( ite->n_value == 1 )
				nValue = 2;
			else
				nValue = 1;
		}
		else
		{
			sprintf(cError,"__CvtNxSubFieldToGenList_Int():不支持获取n_msg_type=%d消息的103点配置",
				m_pCommonMsg->n_msg_type);
			RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
		}

		if( !bGetCfg)
		{
			++ite;
			continue;
		}

		nKod = ___GetGenKod(m_pCommonMsg->n_data_src);

		__SetGenericIntData(nGroup,nEntry,nKod,nValue,GenData,bDpi);

		// 将通用分类数据加入列表
		if( GToGenListMap.count(nGroup) > 0 )    // 该组已存在则获取对应的列表指针
		{
			pGenList = GToGenListMap[nGroup];
		}
		else      // 不存在需新建
		{
			pGenList = new ASDU_GEN_INFO_LIST;
			GToGenListMap[nGroup] = pGenList;
		}

		if( pGenList != NULL )
			pGenList->push_back(GenData);

		++ite;
		pGenList = NULL;
		nGroup = nEntry = nKod = nDataType = 0;
		bDpi  = false;
		bGetCfg = false;
		nValue = 0;
	}

	GenData.vGid.clear();
	return EC_PRO_CVT_SUCCESS;
}

/**
* @brief         根据ASDU10信息结构格式化ASDU10报文体（支持指定RII值）
* @param[in]     ASDU10_INFO &Asdu10Info:asdu10信息结构
* @param[out]    PRO_FRAME_BODY_LIST  & lBody :ASDU规约信息体列表
* @param[in]     u_int8 nRii:返回信息标识符
* @param[in]     int nReserve:备用
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu10FJ::FormatAsdu10BodyAutoUp(ASDU10_INFO &Asdu10Info,PRO_FRAME_BODY_LIST &lBody,u_int8 nRii,int nReserve)
{
	char cError[255] = "";
	int nSrcSize = lBody.size();
	int nEntryNum = 0;  // 总条目数
	GROUP2GENLIST_MAP::iterator iteMap;
	ASDU_GEN_INFO_LIST * pGenList= NULL;

	// 格式化所有组自动上送（使用指定的RII值）
	iteMap = Asdu10Info.GroupToGenListMap.begin();
	while( iteMap != Asdu10Info.GroupToGenListMap.end() )
	{
		_FormatOneGroupAsdu10BodyAutoUp(Asdu10Info,iteMap->first,nRii,lBody);
		pGenList = iteMap->second;
		if( pGenList != NULL )
			nEntryNum += pGenList->size();
		++iteMap;
	}
	sprintf(cError,"FormatAsdu10BodyAutoUp():生成%d条自动上送asdu10,RII=0x%02X,addr=%d,nRii=%d,cpu=%d 总条目数=%d ",
		lBody.size()-nSrcSize,nRii,Asdu10Info.Addr.nAddr,nRii,Asdu10Info.Addr.nCpu,nEntryNum);

	RcdTrcLogWithParentClass(cError,"TNXEcProAsdu10FJ");
	return 0;
}

