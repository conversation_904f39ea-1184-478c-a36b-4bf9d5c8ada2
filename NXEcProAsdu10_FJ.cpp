/**********************************************************************
* NXEcProAsdu10.cpp         author:ml      date:08/11/2025            
*---------------------------------------------------------------------
*  note: ASDU10����ת������ʵ���ļ�                                                                
*  
**********************************************************************/

#include "NXEcProAsdu10_FJ.h"

CCvtDataToXml gCvtXml_10;
/**
* @brief         ��������
* @param[in]     �� 
* @param[out]    ��
* @return        ��
*/
TNXEcProAsdu10FJ::~TNXEcProAsdu10FJ()
{

}

/**
* @brief         ���캯�� 
* @param[in]     INXEcSSModelSeek * pSeekIns:ģ�Ͳ�ѯʵ��
* @param[out]    CLogRecord * pLogRecord:��־����ָ��
* @return        ��
*/
TNXEcProAsdu10FJ::TNXEcProAsdu10FJ(IN INXEcSSModelSeek * pSeekIns,IN CLogRecord * pLogRecord)
	:TNXEcProAsdu10(pSeekIns,pLogRecord)
{
	// ����������
	_SetLogClassName("TNXEcProAsdu10FJ");
	m_pEventMsg = NULL;
	m_pCommonMsg = NULL;
}

/**
* @brief         ����NXͨ����Ϣ����Լ�������ɹ�Լ����б������ͨ����Ϣ���ɹ�Լ����
* @param[in]     NX_COMMON_MESSAGE * pMsg :ͨ����Ϣ�ṹָ��
* @param[in][out]PRO_FRAME_BODY_LIST & lCmd:��Լ����
* @param[out]    PRO_FRAME_BODY_LIST & lResult :��Լ��Ϣ���б�
* @return        int 0-�ɹ� ����ʧ��
*/
int TNXEcProAsdu10FJ::ConvertCommonMsgToPro(IN NX_COMMON_MESSAGE * pMsg,IN OUT PRO_FRAME_BODY_LIST & lCmd,OUT PRO_FRAME_BODY_LIST & lResult)
{
	char cError[255] = "";
	int  nRet = EC_PRO_CVT_FAIL ;
	m_pCommonMsg = pMsg;

	switch(pMsg->n_msg_type)
	{
	case NX_IED_CALL_SG_REP:       // ��ֵ
		nRet = _CvtSgResultToPro( lCmd,lResult );
		break;
	case NX_IED_CALL_SGZONE_REP:   // ��ֵ����
		nRet = _CvtZoneResultToPro(lCmd,lResult );
		break;
	case NX_IED_CALL_SOFTSTRAP_REP:// ��ѹ��
		nRet = _CvtSoftResultToPro(lCmd,lResult );
		break;
	case NX_IED_CALL_ANALOG_REP:   // ģ����
		nRet = _CvtAiResultToPro(lCmd,lResult );
		break;
	case NX_IED_CTRL_SG_CHECK_REP: // ��ֵԤУ
		nRet = _CvtSgCheckResultToPro(lCmd,lResult );
		break;
	case NX_IED_CTRL_SG_EXC_REP:   // ��ִֵ��
		nRet = _CvtSgExcResultToPro(lCmd,lResult);
		break;
	case NX_IED_CTRL_SGZONE_CHECK_REP: // ��ֵ��ԤУ
		nRet = _CvtZoneCheckResultToPro(lCmd,lResult);
		break;
	case NX_IED_CTRL_SGZONE_EXC_REP:   // ��ֵ��ִ��
		nRet = _CvtZoneExcResultToPro(lCmd,lResult);
		break;
	case NX_IED_CTRL_SOFTSTRAP_CHECK_REP: // ��ѹ��ԤУ
		nRet = _CvtSoftCheckResultToPro(lCmd,lResult);
		break;
	case NX_IED_CTRL_SOFTSTRAP_EXC_REP:   // ��ѹ��ִ��
		nRet = _CvtSoftExcResultToPro(lCmd,lResult);
		break;
	case NX_IED_CTRL_HARDSTRAP_CHECK_REP: // Ӳѹ��ԤУ
		nRet = _CvtHardCheckResultToPro(lCmd,lResult);
		break;
	case NX_IED_CTRL_HARDSTRAP_EXC_REP:   // Ӳѹ��ִ��
		nRet = _CvtHardExcResultToPro(lCmd,lResult);
		break;
	case NX_IED_CALL_ROBOTCHECK_REP:   // ������Ѳ��ִ�лظ�
		nRet = _CvtRobertCheckResultToPro(lCmd,lResult);
		break;
	case NX_IED_CALL_61850SRV_READ_REP:
		nRet = _Cvt61850ReadResultToPro(lCmd,lResult);
		break;
	default:
		sprintf(cError,"ConvertCommonMsgToPro()���ݲ�֧��n_msg_type=%d����Ϣ����",pMsg->n_msg_type);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10");
		nRet = EC_PRO_CVT_NOSUPPORT;
		break;
	}
	m_pCommonMsg = NULL;
	return nRet;
}
/**
* @brief         ת��NX��ѹ���λ�¼���Ϣ����Լ�¼��б�
* @param[out]    PRO_FRAME_BODY_LIST  & lBody :��Լ��Ϣ��
* @return        int 0-�ɹ� ����ʧ��
*/
int TNXEcProAsdu10FJ::_CvtSoftReportToPro(OUT PRO_FRAME_BODY_LIST & lBody)
{
	// ת��NX��ϢΪASDU10Info�ṹ

	char cError[255]="";

	ASDU10_INFO Asdu10Info;
	int nCpu = 0;
	int nAddr = 0;
	int nRet = __CvtNxSubFieldToGenList_IntAuToUp(Asdu10Info.GroupToGenListMap,nCpu,nAddr);

	sprintf(cError,"_CvtSoftReportToPro():��ȡ����103��ַΪ��%d,CPUΪ��%d.",
		nAddr,nCpu);
	RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");

	__CvtAsdu10InfoToProBodyAuToUp(nRet,nCpu,nAddr,Asdu10Info,lBody);

	// ����ASDU10�ṹ
	ClearAsdu10Info(Asdu10Info);
	return 0;
}

/**
* @brief         ת��NX��ֵ���仯�¼���Ϣ����Լ�¼��б�
* @param[out]    PRO_FRAME_BODY_LIST  & lBody :��Լ��Ϣ��
* @return        int 0-�ɹ� ����ʧ��
*/
int TNXEcProAsdu10FJ::_CvtZoneChgReportToPro(OUT PRO_FRAME_BODY_LIST & lBody)
{
	// ת��NX��ϢΪASDU10Info�ṹ
	char cError[255]="";
	ASDU10_INFO Asdu10Info;
	int nCpu = 0;
	int nAddr = 0;
	int nRet = __CvtNxSubFieldToGenList_IntAuToUp(Asdu10Info.GroupToGenListMap,nCpu,nAddr);

	sprintf(cError,"_CvtZoneChgReportToPro():��ȡ����103��ַΪ��%d,CPUΪ��%d.",
		nAddr,nCpu);
	RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");

	__CvtAsdu10InfoToProBodyAuToUp(nRet,nCpu,nAddr,Asdu10Info,lBody);

	// ����ASDU10�ṹ
	ClearAsdu10Info(Asdu10Info);
	return 0;
}
/**
* @brief         ת��NX������Ѳ�ӱ����¼���Ϣ����Լ�¼��б�
* @param[out]    PRO_FRAME_BODY_LIST  & lBody :��Լ��Ϣ��
* @return        int 0-�ɹ� ����ʧ��
*/
int TNXEcProAsdu10FJ::_CvtRobertCheckReportToPro(OUT PRO_FRAME_BODY_LIST & lBody)
{
	// ת��NX��ϢΪASDU10Info�ṹ
	char cError[255]="";
	ASDU10_INFO Asdu10Info;
	int nCpu = 0;
	int nAddr = 0;
	int nRet = __CvtNxSubFieldToGenList_IntAuToUp_Robert(Asdu10Info.GroupToGenListMap,nCpu,nAddr);

	sprintf(cError,"_CvtRobertCheckReportToPro():��ȡ����103��ַΪ��%d,CPUΪ��%d.",
		nAddr,nCpu);
	RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");

	__CvtAsdu10InfoToProBodyAuToUp_Rot(nRet,nCpu,nAddr,Asdu10Info,lBody);

	// ����ASDU10�ṹ
	ClearAsdu10Info(Asdu10Info);
	return 0;
}
/**
* @brief         ת��NXͨ����Ϣ�Ӽ����ݣ�ֵΪ���ͣ�Ϊ�����ͨ�÷������ݼ�ӳ���
* @param[out]     OUT GROUP2GENLIST_MAP & GToGenListMap:ͨ�÷������ݼ�ӳ���
* @return        int 0-�ɹ� ����ʧ��
*/
int TNXEcProAsdu10FJ::__CvtNxSubFieldToGenList_IntAuToUp(OUT GROUP2GENLIST_MAP & GToGenListMap,OUT int& nCpu,OUT int& nAddr)
{
	char cError[255]="";
	ASDU_GEN_INFO GenData;     // ͨ�÷������ݽṹ
	ASDU_GEN_INFO_LIST * pGenList = NULL;
	u_int8 nGroup = 0,nEntry = 0,nDataType=0,nKod=0;
	bool  bGetCfg = false,bDpi = false;
	int nValue = 0;
	const IED_TB * pIed = NULL;
	// ת��NX�¼��Ӽ��ṹΪͨ�÷�������
	NX_EVENT_MSG_SUBFILED_LIST::iterator ite = m_pEventMsg->list_subfields.begin();
	while ( ite != m_pEventMsg->list_subfields.end() )
	{
		GenData.vGid.clear();

		nCpu = ite->n_sub_obj_id;

		pIed =  m_pModelSeek->GetIedBasicCfgByID(ite->n_obj_id);
		if( pIed == NULL )
		{
			++ite;
			continue;
		}
		nAddr = pIed->n_outaddr103;

		sprintf(cError,"__CvtNxSubFieldToGenList_Int():��ȡ����103��ַΪ��%d,CPUΪ��%d.",
			nAddr,nCpu);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");

		// ������\��Ŀ�ż�����������Ϣ
		if(  m_pEventMsg->n_msg_type == NX_SYS_EVENT_IED_SGZONE_CHG_REPORT )
		{
			bGetCfg = ___GetZoneDataPoint103CfgAutoUp(ite->n_obj_id,ite->n_sub_obj_id,ite->n_sub_sub_obj_id,nGroup,nEntry,nDataType);
			nValue = ite->n_value;
		}
		else if( m_pEventMsg->n_msg_type == NX_IED_EVENT_SOFTTRAP_REPORT )
		{
			bDpi = true;
			bGetCfg = ___GetSoftDataPoint103CfgAutoUp(ite->n_obj_id,ite->n_sub_obj_id,ite->n_sub_sub_obj_id,nGroup,nEntry,nDataType);
			if( ite->n_value == 1 )
				nValue = 2;
			else
				nValue = 1;
		}
		else
		{
			sprintf(cError,"__CvtNxSubFieldToGenList_Int():��֧�ֻ�ȡn_msg_type=%d��Ϣ��103������",
				m_pEventMsg->n_msg_type);
			RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
		}
		if( !bGetCfg)
		{
			++ite;
			continue;
		}
		nKod = ___GetGenKod(m_pEventMsg->n_data_src);
		__SetGenericIntData(nGroup,nEntry,nKod,nValue,GenData,bDpi);
		// ��ͨ�÷������ݼ����б�
		if( GToGenListMap.count(nGroup) > 0 )    // �����Ѵ������ȡ��Ӧ���б�ָ��
		{
			pGenList = GToGenListMap[nGroup];
		}
		else      // ���������½�
		{
			pGenList = new ASDU_GEN_INFO_LIST;
			GToGenListMap[nGroup] = pGenList;
		}

		if( pGenList != NULL )
			pGenList->push_back(GenData);

		int nflag = -99;

		if (nflag != nGroup)
		{
			sprintf(cError,"__CvtNxSubFieldToGenList_Int():����0����Ŀ,���=%d,nflag=%d.",
				nGroup,nflag);
			RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");

			//�����0��Ŀ,ʱ����Ϣ.
			//װ��ʱ��
			string strDevTm;
			CTimeConvert DevTmCvt(ite->n_curvalueutctm,ite->n_curms);
			DevTmCvt.GetCP56TIMe(strDevTm);
			//��վ����ʱ��
			time_t nowtime = time(NULL);
			string strRevTm;
			CTimeConvert RevTmCvt(nowtime,0);
			RevTmCvt.GetCP56TIMe(strRevTm);
			string strTime14 = strDevTm + strRevTm;
			__SetGenericTimeData(nGroup,0,1,strTime14,GenData);
			pGenList->push_front(GenData);

			nflag = nGroup;
		}

		++ite;
		pGenList = NULL;
		nGroup = nEntry = nKod = nDataType = 0;
		bDpi  = false;
		bGetCfg = false;
		nValue = 0;
	}

	sprintf(cError,"__CvtNxSubFieldToGenList_Int():��ȡ����103��ַΪ��%d,CPUΪ��%d.",
		nAddr,nCpu);
	RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");

	GenData.vGid.clear();
	return EC_PRO_CVT_SUCCESS;
}
/**
* @brief         ת��NXͨ����Ϣ�Ӽ����ݣ�������ר�ã�Ϊ�����ͨ�÷������ݼ�ӳ���
* @param[out]     OUT GROUP2GENLIST_MAP & GToGenListMap:ͨ�÷������ݼ�ӳ���
* @return        int 0-�ɹ� ����ʧ��
*/
int TNXEcProAsdu10FJ::__CvtNxSubFieldToGenList_IntAuToUp_Robert(OUT GROUP2GENLIST_MAP & GToGenListMap,OUT int& nCpu,OUT int& nAddr)
{
	char cError[255]="";
	ASDU_GEN_INFO GenData;     // ͨ�÷������ݽṹ
	ASDU_GEN_INFO_LIST * pGenList = NULL;
	u_int8 nGroup = 0,nEntry = 0,nDataType=0,nKod=0;
	bool  bGetCfg = false,bDpi = false;
	char cValue[255]="";
	const IED_TB * pIed = NULL;
	// ת��NX�¼��Ӽ��ṹΪͨ�÷�������
	NX_EVENT_MSG_SUBFILED_LIST::iterator ite = m_pEventMsg->list_subfields.begin();
	while ( ite != m_pEventMsg->list_subfields.end() )
	{
		GenData.vGid.clear();

		nCpu = 1;

		pIed =  m_pModelSeek->GetIedBasicCfgByID(m_pEventMsg->n_event_obj);
		if( pIed == NULL )
		{
			++ite;
			continue;
		}
		nAddr = pIed->n_outaddr103;

		sprintf(cError,"__CvtNxSubFieldToGenList_IntAuToUp_Robert():��ȡ����103��ַΪ��%d,CPUΪ��%d.",
			nAddr,nCpu);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");

		// ������\��Ŀ�ż�����������Ϣ
		bGetCfg = ___GetGroupCfg(ite->c_value_f,nGroup,nEntry);
		sprintf(cValue,"%s",ite->c_field_name);
		printf("@@@@@@@@@@@@@@@@@@������Ѳ�ӵ�װ�ã�%s,�ļ���%s.\n",ite->c_value_f,ite->c_field_name);
		printf("@@@@@@@@@@@@@@@@@@��ţ�%d,��Ŀ�ţ�%d.\n",nGroup,nEntry);
		if( !bGetCfg)
		{
			++ite;
			continue;
		}
		printf("--------------------------->\n");
		nKod = 1;
		__SetGenericStrData(nGroup,nEntry,nKod,cValue,GenData);
		// ��ͨ�÷������ݼ����б�
		if( GToGenListMap.count(nGroup) > 0 )    // �����Ѵ������ȡ��Ӧ���б�ָ��
		{
			pGenList = GToGenListMap[nGroup];
		}
		else      // ���������½�
		{
			pGenList = new ASDU_GEN_INFO_LIST;
			GToGenListMap[nGroup] = pGenList;
		}

		if( pGenList != NULL )
			pGenList->push_back(GenData);

		++ite;
		pGenList = NULL;
		nGroup = nEntry = nKod = nDataType = 0;
		bDpi  = false;
		bGetCfg = false;
	}

	sprintf(cError,"__CvtNxSubFieldToGenList_IntAuToUp_Robert():��ȡ����103��ַΪ��%d,CPUΪ��%d.",
		nAddr,nCpu);
	RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");

	GenData.vGid.clear();
	return EC_PRO_CVT_SUCCESS;
}

/**
* @brief         ����ASDU10��Ϣ���ٻ��������Ϊ��Լ�������ʽ�б�
* @param[in]     nCvtResult:ת����� �ɹ���ʧ��
* @param[in]     ASDU10_INFO &Asdu10Info:asdu10��Ϣ�ṹ
* @param[in]    PRO_FRAME_BODY_LIST& lCmd:�����б�
* @param[out]    PRO_FRAME_BODY_LIST& lResult:���������б�
* @return        int 0-�ɹ� ����ʧ��
*/
int TNXEcProAsdu10FJ::__CvtAsdu10InfoToProBodyAuToUp(IN int nCvtResult,IN int nCpu,IN int nAddr,IN ASDU10_INFO & Asdu10Info,OUT PRO_FRAME_BODY_LIST & lResult)
{
	char cError[255] = "";
	// ��ӡ��־
	string strLog="ת��NX��Ϣ��" + _get_eventmsg_desc(*m_pEventMsg) + "��asdu10�ṹ���";

	Asdu10Info.Addr.nCpu = nCpu;
	Asdu10Info.Addr.nAddr = nAddr;
	if( Asdu10Info.GroupToGenListMap.size() > 0 )
	{
		strLog += ",�������:\n";
		sprintf(cError,"addr =%d,cpu =%d",Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu);
		strLog += cError;
		GROUP2GENLIST_MAP::iterator iteMap = Asdu10Info.GroupToGenListMap.begin();
		while(iteMap != Asdu10Info.GroupToGenListMap.end() )
		{
			_ZERO_MEM(cError,255);
			sprintf(cError,"���(%d)��%d����Ŀ;",iteMap->first,iteMap->second->size());
			strLog += cError;
			++iteMap;
		}
	}

	RcdTrcLogWithParentClass(strLog.c_str(),"TNXEcProAsdu10FJ");

	// ��ASDU10��Ϣ�ṹ��ʽΪASDU10��Լ��ʽ
	// ������Ϣ����ѡ���Ӧ��RIIֵ
	u_int8 nRii = 0;  // Ĭ��RIIֵΪ0������ԭ����Ϊ��
	if (m_pEventMsg != NULL) {
		switch(m_pEventMsg->n_msg_type) {
			case NX_IED_EVENT_SOFTTRAP_REPORT:         // ��������Ϣ
				nRii = 0xFD;  // ��������Ϣ֡ RII = 253 (FDH)
				break;
			case NX_SYS_EVENT_IED_SGZONE_CHG_REPORT:   // ��ֵ���仯��Ϣ
				nRii = 0xFE;  // ��ֵ���仯��Ϣ֡ RII = 254 (FEH)
				break;
			default:
				nRii = 0;     // ������Ϣ����ʹ��Ĭ��RII=0
				break;
		}
	}

	FormatAsdu10BodyAutoUp(Asdu10Info,lResult,nRii);

	_ZERO_MEM(cError,255);
	sprintf(cError,"NX��Ϣ���:%s ת��Ϊ%d��asdu10",_get_eventmsg_desc(*m_pEventMsg).c_str(),lResult.size());
	RcdTrcLogWithParentClass(cError,"TNXEcProAsdu10FJ");

	return 0;
}
/**
* @brief         ����ASDU10��Ϣ���ٻ��������Ϊ��Լ�������ʽ�б�
* @param[in]     nCvtResult:ת����� �ɹ���ʧ��
* @param[in]     ASDU10_INFO &Asdu10Info:asdu10��Ϣ�ṹ
* @param[in]    PRO_FRAME_BODY_LIST& lCmd:�����б�
* @param[out]    PRO_FRAME_BODY_LIST& lResult:���������б�
* @return        int 0-�ɹ� ����ʧ��
*/
int TNXEcProAsdu10FJ::__CvtAsdu10InfoToProBodyAuToUp_Rot(IN int nCvtResult,IN int nCpu,IN int nAddr,IN ASDU10_INFO & Asdu10Info,OUT PRO_FRAME_BODY_LIST & lResult)
{
	char cError[255] = "";
	// ��ӡ��־
	string strLog="ת��NX��Ϣ��" + _get_eventmsg_desc(*m_pEventMsg) + "��asdu10�ṹ���";

	Asdu10Info.Addr.nCpu = nCpu;
	Asdu10Info.Addr.nAddr = nAddr;
	if( Asdu10Info.GroupToGenListMap.size() > 0 )
	{
		strLog += ",�������:\n";
		sprintf(cError,"addr =%d,cpu =%d",Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu);
		strLog += cError;
		GROUP2GENLIST_MAP::iterator iteMap = Asdu10Info.GroupToGenListMap.begin();
		while(iteMap != Asdu10Info.GroupToGenListMap.end() )
		{
			_ZERO_MEM(cError,255);
			sprintf(cError,"���(%d)��%d����Ŀ;",iteMap->first,iteMap->second->size());
			strLog += cError;
			++iteMap;
		}
	}

	RcdTrcLogWithParentClass(strLog.c_str(),"TNXEcProAsdu10FJ");

	// ��ASDU10��Ϣ�ṹ��ʽΪASDU10��Լ��ʽ

	FormatAsdu10BodyAutoUp_Rot(Asdu10Info,lResult);

	_ZERO_MEM(cError,255);
	sprintf(cError,"NX��Ϣ���:%s ת��Ϊ%d��asdu10",_get_eventmsg_desc(*m_pEventMsg).c_str(),lResult.size());
	RcdTrcLogWithParentClass(cError,"TNXEcProAsdu10FJ");

	return 0;
}
/**
* @brief         ����ASDU10��Ϣ�ṹ��ʽ��ASDU10������
* @param[in]     ASDU10_INFO &Asdu10Info:asdu10��Ϣ�ṹ
* @param[out]    PRO_FRAME_BODY_LIST  & lBody :ASDU��Լ��Ϣ���б�
* @param[in]     int nReserve:����
* @return        int 0-�ɹ� ����ʧ��
*/
int TNXEcProAsdu10FJ::FormatAsdu10BodyAutoUp(IN ASDU10_INFO &Asdu10Info,OUT PRO_FRAME_BODY_LIST &lBody,IN int nReserve)
{
	char cError[255] = "";
	int nSrcSize = lBody.size();
	int nEntryNum = 0;  // ����Ŀ��
	GROUP2GENLIST_MAP::iterator iteMap;
	ASDU_GEN_INFO_LIST * pGenList= NULL;


	// ��ʽ���������Զ�����
	iteMap = Asdu10Info.GroupToGenListMap.begin();
	while( iteMap != Asdu10Info.GroupToGenListMap.end() )
	{
		_FormatOneGroupAsdu10BodyAutoUp(Asdu10Info,iteMap->first,0,lBody);
		pGenList = iteMap->second;
		if( pGenList != NULL )
			nEntryNum += pGenList->size();
		++iteMap;
	}
	sprintf(cError,"FormatAsdu10BodyAutoUp():����%d���Զ�����asdu10,addr=%d cpu=%d ����Ŀ��=%d ",
		lBody.size()-nSrcSize,Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu,nEntryNum);

	RcdTrcLogWithParentClass(cError,"TNXEcProAsdu10FJ");
	return 0;
}
/**
* @brief         ����ASDU10��Ϣ�ṹ��ʽ��ASDU10������
* @param[in]     ASDU10_INFO &Asdu10Info:asdu10��Ϣ�ṹ
* @param[out]    PRO_FRAME_BODY_LIST  & lBody :ASDU��Լ��Ϣ���б�
* @param[in]     int nReserve:����
* @return        int 0-�ɹ� ����ʧ��
*/
int TNXEcProAsdu10FJ::FormatAsdu10BodyAutoUp_Rot(IN ASDU10_INFO &Asdu10Info,OUT PRO_FRAME_BODY_LIST &lBody,IN int nReserve)
{
	char cError[255] = "";
	int nSrcSize = lBody.size();
	int nEntryNum = 0;  // ����Ŀ��
	GROUP2GENLIST_MAP::iterator iteMap;
	ASDU_GEN_INFO_LIST * pGenList= NULL;


	// ��ʽ���������Զ�����
	iteMap = Asdu10Info.GroupToGenListMap.begin();
	while( iteMap != Asdu10Info.GroupToGenListMap.end() )
	{
		_FormatOneGroupAsdu10BodyAutoUp_Rot(Asdu10Info,iteMap->first,0,lBody);
		pGenList = iteMap->second;
		if( pGenList != NULL )
			nEntryNum += pGenList->size();
		++iteMap;
	}
	sprintf(cError,"FormatAsdu10BodyAutoUp():����%d���Զ�����asdu10,addr=%d cpu=%d ����Ŀ��=%d ",
		lBody.size()-nSrcSize,Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu,nEntryNum);

	RcdTrcLogWithParentClass(cError,"TNXEcProAsdu10FJ");
	return 0;
}
/**
* @brief         ����ASDU10��Ϣ�ṹ��ʽ��ָ����ŵ�ASDU10������
* @param[in]     ASDU10_INFO &Asdu10Info:asdu10��Ϣ�ṹ
* @param[in]     u_int8 nGroup:ָ�����
* @param[in]     u_int8 nRii:������Ϣ��ʶ��
* @param[out]    PRO_FRAME_BODY_LIST  & lBody :ASDU��Լ��Ϣ���б�
* @return        int 0-�ɹ� ����ʧ��
*/
int TNXEcProAsdu10FJ::_FormatOneGroupAsdu10BodyAutoUp(IN ASDU10_INFO &Asdu10Info,IN u_int8 nGroup,IN u_int8 nRii,OUT PRO_FRAME_BODY_LIST &lBody)
{
	char cError[255] = "";
	string strLog;
	u_int8 nNgd = 0;            // ͨ�÷������ݼ���Ŀ
	u_int8 nCounter=0,nTmpValue=0; // ���Ƽ�����λ
	PRO_FRAME_BODY ProBody;     // ��Լ������
	ASDU_GEN_INFO_LIST * pGenericList=NULL;
	// ��ȡͨ�÷������ݼ��б�
	if( Asdu10Info.InfoObj.nInf == 0xf0 )  //������Ӧ
	{
		GROUP2GENLIST_MAP::iterator iteMap = Asdu10Info.GroupToGenListMap.begin();
		pGenericList = iteMap->second;
	}
	else
		pGenericList = Asdu10Info.GroupToGenListMap[nGroup];
	if( pGenericList == NULL )
	{
		sprintf(cError,"_FormatOneGroupAsdu10BodyAutoUp():stationAddr =%d addr=%d cpu=%d inf=%d��asdu10��Ϣ�ṹ��group=%d�����ݼ��б�ΪNULL,����asdu10������ʧ��",
			Asdu10Info.Addr.nSubstationAdd,Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu,Asdu10Info.InfoObj.nInf,nGroup);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
		return EC_PRO_CVT_FAIL;
	}
	// �ж����ݼ�����
	int nGenSize = pGenericList->size();
	if( nGenSize <= 0 )
	{
		sprintf(cError,"_FormatOneGroupAsdu10BodyAutoUp():stationAddr =%d addr=%d cpu=%d inf=%d��asdu10��Ϣ�ṹ��group=%d�����ݼ�����Ϊ%d,����asdu10������ʧ��",
			Asdu10Info.Addr.nSubstationAdd,Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu,Asdu10Info.InfoObj.nInf,nGroup,nGenSize);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
		return EC_PRO_CVT_FAIL;
	}

	// ��ñ��վ��ַ
// 	const SUBSTATION_TB * pStation = m_pModelSeek->GetSubStationBasicCfg();
// 	if( pStation == NULL )
// 		return EC_PRO_CVT_FAIL;
// 	int16 nSubstationAdd = pStation->n_outaddr103;

	// ���ù�Լ֡�̶�����
	ProBody.nType =  0x0A;   
	ProBody.nVsq  =  0x81;    
	ProBody.nCot  =  0x01;
	ProBody.nSubstationAdd = Asdu10Info.Addr.nSubstationAdd;
// 	ProBody.nAddr = Asdu10Info.Addr.nAddr;
// 	ProBody.nCpu  = Asdu10Info.Addr.nCpu;
	ProBody.nAddr = Asdu10Info.Addr.nAddr;
	ProBody.nCpu  = Asdu10Info.Addr.nCpu;
	ProBody.nZone = Asdu10Info.Addr.nZone;
	ProBody.nFun  = 0xFE;//m_nFun;
	ProBody.nInf  = 0xF1;//m_nInf;

	// ���ÿɱ䲿��
	ProBody.vVarData.push_back(nRii);          // ������Ϣ��ʶ��
	ProBody.vVarData.push_back(nNgd);          // NGD (��ʱ���룬����������Ŀ��������޸�)
	ASDU_GEN_INFO_LIST::iterator ite = pGenericList->begin();
	while( ite != pGenericList->end() )
	{
		// �жϿɱ䲿���Ƿ�Խ��
		if( ( (ProBody.vVarData.size()+ 6 + ite->vGid.size() ) > GetAsduVarDataMaxLen() ) || 
			( nNgd >= ASDU10_MAX_NGD ) 
			)
		{
			// ���ü�����λ
			nTmpValue = (nCounter & 0x01) << 6; // ȡ���λ,Ȼ������6λ
			nNgd = nNgd | nTmpValue;
			nCounter ++ ;
			nTmpValue = 0;
			// ���ú���״̬λ
			nNgd = nNgd | 0x80;
			// �������ÿɱ䲿�ֵ�NGD��Ŀ
			ProBody.vVarData[1] = nNgd;
			// ��������
			lBody.push_back(ProBody);
			// ����ɱ䲿������
			ProBody.vVarData[1] = 0;
			ProBody.vVarData.erase(ProBody.vVarData.begin()+2,ProBody.vVarData.end());
			nNgd = 0;
		}
		// ������ݼ�
		ProBody.vVarData.push_back(ite->nGroup);
		ProBody.vVarData.push_back(ite->nEntry);
		ProBody.vVarData.push_back(ite->nKod);
		ProBody.vVarData.push_back(ite->nDataType);
		ProBody.vVarData.push_back(ite->nDataSize);
		ProBody.vVarData.push_back(ite->nDataNumber);
		if( ite->vGid.size() > 0 )
			ProBody.vVarData.insert(ProBody.vVarData.end(),ite->vGid.begin(),ite->vGid.end());
		// NGD��Ŀ����
		nNgd ++;
		nGenSize --;
		if ( 0 == nGenSize )  // ���һ֡
		{
			// ���ü�����λ
			nTmpValue = (nCounter & 0x01) << 6; // ȡ���λ,Ȼ������6λ
			nNgd = nNgd | nTmpValue;
			// �������ÿɱ䲿�ֵ�NGD��Ŀ
			ProBody.vVarData[1] = nNgd;
			// ��������
			lBody.push_back(ProBody);
			// ����ɱ䲿������
			ProBody.vVarData.clear();
			nNgd = 0;
			break;
		}
		++ite;
	}
	ProBody.vVarData.clear();
	return 0;
}
/**
* @brief         ����ASDU10��Ϣ�ṹ��ʽ��ָ����ŵ�ASDU10������
* @param[in]     ASDU10_INFO &Asdu10Info:asdu10��Ϣ�ṹ
* @param[in]     u_int8 nGroup:ָ�����
* @param[in]     u_int8 nRii:������Ϣ��ʶ��
* @param[out]    PRO_FRAME_BODY_LIST  & lBody :ASDU��Լ��Ϣ���б�
* @return        int 0-�ɹ� ����ʧ��
*/
int TNXEcProAsdu10FJ::_FormatOneGroupAsdu10BodyAutoUp_Rot(IN ASDU10_INFO &Asdu10Info,IN u_int8 nGroup,IN u_int8 nRii,OUT PRO_FRAME_BODY_LIST &lBody)
{
	char cError[255] = "";
	string strLog;
	u_int8 nNgd = 0;            // ͨ�÷������ݼ���Ŀ
	u_int8 nCounter=0,nTmpValue=0; // ���Ƽ�����λ
	PRO_FRAME_BODY ProBody;     // ��Լ������
	ASDU_GEN_INFO_LIST * pGenericList=NULL;
	// ��ȡͨ�÷������ݼ��б�
	if( Asdu10Info.InfoObj.nInf == 0xf0 )  //������Ӧ
	{
		GROUP2GENLIST_MAP::iterator iteMap = Asdu10Info.GroupToGenListMap.begin();
		pGenericList = iteMap->second;
	}
	else
		pGenericList = Asdu10Info.GroupToGenListMap[nGroup];
	if( pGenericList == NULL )
	{
		sprintf(cError,"_FormatOneGroupAsdu10BodyAutoUp():stationAddr =%d addr=%d cpu=%d inf=%d��asdu10��Ϣ�ṹ��group=%d�����ݼ��б�ΪNULL,����asdu10������ʧ��",
			Asdu10Info.Addr.nSubstationAdd,Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu,Asdu10Info.InfoObj.nInf,nGroup);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
		return EC_PRO_CVT_FAIL;
	}
	// �ж����ݼ�����
	int nGenSize = pGenericList->size();
	if( nGenSize <= 0 )
	{
		sprintf(cError,"_FormatOneGroupAsdu10BodyAutoUp():stationAddr =%d addr=%d cpu=%d inf=%d��asdu10��Ϣ�ṹ��group=%d�����ݼ�����Ϊ%d,����asdu10������ʧ��",
			Asdu10Info.Addr.nSubstationAdd,Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu,Asdu10Info.InfoObj.nInf,nGroup,nGenSize);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
		return EC_PRO_CVT_FAIL;
	}

	// ��ñ��վ��ַ
	// 	const SUBSTATION_TB * pStation = m_pModelSeek->GetSubStationBasicCfg();
	// 	if( pStation == NULL )
	// 		return EC_PRO_CVT_FAIL;
	// 	int16 nSubstationAdd = pStation->n_outaddr103;

	// ���ù�Լ֡�̶�����
	ProBody.nType =  0x0A;   
	ProBody.nVsq  =  0x81;    
	ProBody.nCot  =  0x01;
	ProBody.nSubstationAdd = Asdu10Info.Addr.nSubstationAdd;
	// 	ProBody.nAddr = Asdu10Info.Addr.nAddr;
	// 	ProBody.nCpu  = Asdu10Info.Addr.nCpu;
	ProBody.nAddr = Asdu10Info.Addr.nAddr;
	ProBody.nCpu  = Asdu10Info.Addr.nCpu;
	ProBody.nZone = Asdu10Info.Addr.nZone;
	ProBody.nFun  = 0xFE;//m_nFun;
	ProBody.nInf  = 0xF2;//m_nInf;

	// ���ÿɱ䲿��
	ProBody.vVarData.push_back(nRii);          // ������Ϣ��ʶ��
	ProBody.vVarData.push_back(nNgd);          // NGD (��ʱ���룬����������Ŀ��������޸�)
	ASDU_GEN_INFO_LIST::iterator ite = pGenericList->begin();
	while( ite != pGenericList->end() )
	{
		// �жϿɱ䲿���Ƿ�Խ��
		if( ( (ProBody.vVarData.size()+ 6 + ite->vGid.size() ) > GetAsduVarDataMaxLen() ) || 
			( nNgd >= ASDU10_MAX_NGD ) 
			)
		{
			// ���ü�����λ
			nTmpValue = (nCounter & 0x01) << 6; // ȡ���λ,Ȼ������6λ
			nNgd = nNgd | nTmpValue;
			nCounter ++ ;
			nTmpValue = 0;
			// ���ú���״̬λ
			nNgd = nNgd | 0x80;
			// �������ÿɱ䲿�ֵ�NGD��Ŀ
			ProBody.vVarData[1] = nNgd;
			// ��������
			lBody.push_back(ProBody);
			// ����ɱ䲿������
			ProBody.vVarData[1] = 0;
			ProBody.vVarData.erase(ProBody.vVarData.begin()+2,ProBody.vVarData.end());
			nNgd = 0;
		}
		// ������ݼ�
		ProBody.vVarData.push_back(ite->nGroup);
		ProBody.vVarData.push_back(ite->nEntry);
		ProBody.vVarData.push_back(ite->nKod);
		ProBody.vVarData.push_back(ite->nDataType);
		ProBody.vVarData.push_back(ite->nDataSize);
		ProBody.vVarData.push_back(ite->nDataNumber);
		if( ite->vGid.size() > 0 )
			ProBody.vVarData.insert(ProBody.vVarData.end(),ite->vGid.begin(),ite->vGid.end());
		// NGD��Ŀ����
		nNgd ++;
		nGenSize --;
		if ( 0 == nGenSize )  // ���һ֡
		{
			// ���ü�����λ
			nTmpValue = (nCounter & 0x01) << 6; // ȡ���λ,Ȼ������6λ
			nNgd = nNgd | nTmpValue;
			// �������ÿɱ䲿�ֵ�NGD��Ŀ
			ProBody.vVarData[1] = nNgd;
			// ��������
			lBody.push_back(ProBody);
			// ����ɱ䲿������
			ProBody.vVarData.clear();
			nNgd = 0;
			break;
		}
		++ite;
	}
	ProBody.vVarData.clear();
	return 0;
}
/**
* @brief         ��ö�ֵ������Ϣ���103����
* @param[in]     nGroup:��Ϣ����
* @param[out]    nGroup:���
* @param[out]    nItem: ��Ŀ��
* @param[out]    nDataType:��������
* @return        bool:true-�ɹ� false-ʧ��
*/
bool TNXEcProAsdu10FJ::___GetZoneDataPoint103CfgAutoUp(IN int nIed,IN int nLd,IN int nID,OUT u_int8 &nGroup,OUT u_int8 &nEntry,OUT u_int8 &nDataType)
{
	char cError[255] = "";
	const SGZONE_TB * pTb = NULL;
	pTb = m_pModelSeek->GetIedZoneCfg(nIed,nLd,nID);
	if( pTb == NULL )
	{
		sprintf(cError,"___GetZoneDataPoint103Cfg():��ȡIedId=%d,LD=%d,ID=%d������ʧ��",
			nIed,nLd,nID);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
		return false;
	}
	nGroup = pTb->n_outgroup103;
	nEntry = pTb->n_outitem103;
	nDataType = VALUE_INT;
	return true;
}

/**
* @brief         �����ѹ����Ϣ���103����
* @param[in]     nGroup:��Ϣ����
* @param[out]    nGroup:���
* @param[out]    nItem: ��Ŀ��
* @param[out]    nDataType:��������
* @return        bool:true-�ɹ� false-ʧ��
*/
bool TNXEcProAsdu10FJ::___GetSoftDataPoint103CfgAutoUp(IN int nIed,IN int nLd,IN int nID,OUT u_int8 &nGroup,OUT u_int8 &nEntry,OUT u_int8 &nDataType)
{
	char cError[255] = "";
	const STRAP_TB * pTb = NULL;
	pTb = m_pModelSeek->GetIedSoftStrapCfg(nIed,nLd,nID);
	if( pTb == NULL )
	{
		sprintf(cError,"___GetSoftDataPoint103Cfg():��ȡIedId=%d,LD=%d,ID=%d������ʧ��",
			nIed,nLd,nID);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
		return false;
	}
	nGroup = pTb->n_outgroup103;
	nEntry = pTb->n_outitem103;
	nDataType = VALUE_INT;
	return true;
}
/**
* @brief         ����ASDU10��Ϣ����Ϣ����ת��Ϊָ����NXͨ����Ϣ�ṹ
* @param[in]     int nMsgType:��Ϣ����
* @param[in]     ASDU10_INFO &Asdu10Info:asdu10��Ϣ�ṹ
* @param[out]    NX_COMMON_MESSAGE &CommonMsg:ͨ����Ϣ
* @return        int 0-�ɹ� ����ʧ��
*/
//�������ֱ��ʹ�û����еģ�������������
//int TNXEcProAsdu10FJ::__CvtAsdu10InfoStructToCommonMsg(IN int nMsgType,IN ASDU10_INFO & Asdu10Info,OUT NX_COMMON_MESSAGE &CommonMsg)
//{
//	char cError[255]="";
//	// ��õ�ַ��Ϣ
//	const IED_TB * pIedTb = m_pModelSeek->GetIedBasicCfgByAddr103(Asdu10Info.Addr.nAddr);
//	if( pIedTb == NULL )
//	{
//		return EC_PRO_CVT_FAIL;
//	}
//
//	int nBack = (pIedTb->n_obj_id*1000)+Asdu10Info.Addr.nCpu;
//	int nRealIed,nRealCpu;
//	if (0 !=__DBQueryIedByBack(nBack,nRealIed,nRealCpu))
//	{
//		sprintf(cError,"__CvtAsdu10InfoStructToCommonMsg():��ȡѹ�屸���ֶ�1[%d]����ʵIEDʧ��.",nBack);
//		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
//		return EC_PRO_CVT_FAIL;
//	}
//	
//	CommonMsg.n_msg_topic = NX_TOPIC_COMMAND;
//	CommonMsg.n_msg_type  = nMsgType;
//	CommonMsg.n_obj_id    = nRealIed;
//	CommonMsg.n_obj_type  = NX_OBJ_TYPE_NX_IED;
//	CommonMsg.n_sub_obj_id= nRealCpu;
//	CommonMsg.n_sub_sub_obj_id = Asdu10Info.Addr.nZone;
//	CommonMsg.n_data_src = 2;  // ��ʾ��������Զ����վ
//	CommonMsg.b_lastmsg  = true;
//
//	// ת��NX_SUBFIELDS
//	NX_COMMON_MSG_SUBFILED_LIST * pNxSubList = &CommonMsg.list_subfields;
//	GROUP2GENLIST_MAP::iterator iteMap = Asdu10Info.GroupToGenListMap.begin();
//	ASDU_GEN_INFO_LIST * pGenList = NULL;
//	ASDU_GEN_INFO_LIST::iterator ite;
//	NX_COMMON_FIELD_STRUCT tmpSubField;
//	string strValue;
//	int nID = 0;
//
//	while( iteMap != Asdu10Info.GroupToGenListMap.end() )
//	{
//		pGenList = iteMap->second;
//		if( pGenList == NULL )
//		{
//			++iteMap;
//			continue;
//		}
//		// ����ͨ�÷������ݼ��б�
//		ite = pGenList->begin();
//		while (ite != pGenList->end())
//		{
//			tmpSubField.n_field_id = -1;
//			_ZERO_MEM(tmpSubField.c_value,sizeof(tmpSubField.c_value));
//			tmpSubField.n_value = -1;
//			strValue.clear();
//			nID = 0;
//
//			// ��ͨ�÷�����������ת��Ϊ�ַ�ֵ
//			strValue = ___CvtGenericValueToStr(*ite);
//
//			// ������š���Ŀ�š�IED_ID��cpu�Ż�ȡ��Ϣ����
//			switch( nMsgType )
//			{
//			case NX_IED_CALL_ANALOG_REP:
//				memcpy(tmpSubField.c_value,strValue.c_str(),_MIN_VAL(strValue.size(),sizeof(tmpSubField.c_value)-1));
//				nID = ___GetAiPointIDBy103Info(pIedTb->n_obj_id,Asdu10Info.Addr.nCpu,ite->nGroup,ite->nEntry);
//				break;
//			case NX_IED_CALL_SG_REP:
//			case NX_IED_CTRL_SG_CHECK_ASK:
//			case NX_IED_CTRL_SG_EXC_ASK:
//				memcpy(tmpSubField.c_value,strValue.c_str(),_MIN_VAL(strValue.size(),sizeof(tmpSubField.c_value)-1));
//				nID =___GetSgPointIDBy103Info(pIedTb->n_obj_id,Asdu10Info.Addr.nCpu,ite->nGroup,ite->nEntry);
//				break;
//			case NX_IED_CALL_SGZONE_REP:
//			case NX_IED_CTRL_SGZONE_CHECK_ASK:
//			case NX_IED_CTRL_SGZONE_EXC_ASK:
//				tmpSubField.n_value = atoi(strValue.c_str());
//				nID =___GetZonePointIDBy103Info(pIedTb->n_obj_id,Asdu10Info.Addr.nCpu,ite->nGroup,ite->nEntry);
//				break;
//			case NX_IED_CALL_SOFTSTRAP_REP:
//			case NX_IED_CTRL_SOFTSTRAP_CHECK_ASK:
//			case NX_IED_CTRL_SOFTSTRAP_EXC_ASK:
//				tmpSubField.n_value = atoi(strValue.c_str());
//				nID =___GetSoftPointIDBy103Info(pIedTb->n_obj_id,Asdu10Info.Addr.nCpu,ite->nGroup,ite->nEntry);
//				break;
//			case NX_IED_CALL_HARDSTRAP_REP:
//			case NX_IED_CTRL_HARDSTRAP_CHECK_ASK:
//			case NX_IED_CTRL_HARDSTRAP_EXC_ASK:
//				tmpSubField.n_value = atoi(strValue.c_str());
//				nID =___GetHardPointIDBy103Info(nRealIed,nRealCpu,ite->nGroup,ite->nEntry);
//				break;
//			default:
//				nID = -1;
//				break;
//			}
//			if( nID != -1 )
//			{
//				tmpSubField.n_field_id = nID;
//				// ����NX�Ӽ��б�
//				pNxSubList->push_back(tmpSubField);
//			}
//			++ite;
//		} // while (ite != pGenList->end())
//
//		pGenList = NULL;
//		++iteMap;
//	} // while( iteMap != Asdu10Info.GroupToGenListMap.end() )
//
//	return EC_PRO_CVT_SUCCESS;
//}
/**
* @brief        ���Ӳѹ����Ϣ����ڲ�ID���
* @param[in]    nIedId:�豸ID
* @param[in]    nCPU:�豸CPU��
* @param[in]    nGroup:���
* @param[in]    nItem: ��Ŀ��
* @return       int :��Ϣ��ID ,����ȡʧ��ʱ����-1
*/
int TNXEcProAsdu10FJ::___GetHardPointIDBy103Info(IN int nIedId,IN int nCpu,IN u_int8 nGroup,IN u_int8 nEntry)
{
	char cError[255] = "";
	const STRAP_TB * pTb = NULL;
	pTb = m_pModelSeek->GetIedDiCfg(nIedId,nCpu,nGroup,nEntry);
	if( pTb == NULL )
	{
		sprintf(cError,"___GetHardPointIDBy103Info():��ȡIedId=%d,LD=%d,group=%d entry=%d������ʧ��",
			nIedId,nCpu,nGroup,nEntry);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10");
		return -1;
	}

	return pTb->n_strap_code;
}
/**
* @brief         ����ASDU10��Ϣת��ΪԶ������ԤУͨ����Ϣ
* @param[in]     IN int nGroupType:���������
* @param[in]     ASDU10_INFO &Asdu10Info:asdu10��Ϣ�ṹ
* @param[out]    NX_COMMON_MSG_LIST& lMsg:ͨ����Ϣ�б�
* @return        int 0-�ɹ� ����ʧ��
*/
int TNXEcProAsdu10FJ::_CvtAsdu10InfoToRCtlCheck(IN int nGroupType,IN ASDU10_INFO & Asdu10Info,OUT NX_COMMON_MSG_LIST& lMsg)
{
	char cError[255] = "";
	int nRet = EC_PRO_CVT_FAIL;
	switch(nGroupType)
	{
	case EC_GROUP_SG:        // ��ֵ�޸�
		nRet = _CvtAsdu10ToSgCheck(Asdu10Info,lMsg);
		break;
	case EC_GROUP_SOFT:      // ��ѹ��Ͷ��
		nRet = _CvtAsdu10ToSofCheck(Asdu10Info,lMsg);
		break;
	case EC_GROUP_ZONE:      // �����л�
		nRet = _CvtAsdu10ToZoneCheck(Asdu10Info,lMsg);
		break;
	case EC_GROUP_HARD:      // Ӳѹ��Ͷ��
		nRet = _CvtAsdu10ToHardCheck(Asdu10Info,lMsg);
		break;
	default:
		sprintf(cError,"_CvtAsdu10InfoToRCtlCheck()���յ��Ŀ�������������Ϊ:%d,��֧�ִ���",nGroupType);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
		nRet = EC_PRO_CVT_NOSUPPORT;
		break;
	}
	return nRet;
}
/**
* @brief         ����ASDU10��Ϣת��ΪԶ������ִ��ͨ����Ϣ
* @param[in]     IN int nGroupType:���������
* @param[in]     ASDU10_INFO &Asdu10Info:asdu10��Ϣ�ṹ
* @param[out]    NX_COMMON_MSG_LIST& lMsg:ͨ����Ϣ�б�
* @return        int 0-�ɹ� ����ʧ��
*/
int TNXEcProAsdu10FJ::_CvtAsdu10InfoToRCtlExc(IN int nGroupType,IN ASDU10_INFO & Asdu10Info,OUT NX_COMMON_MSG_LIST& lMsg)
{
	char cError[255] = "";
	int nRet = EC_PRO_CVT_FAIL;
	switch(nGroupType)
	{
	case EC_GROUP_SG:        // ��ֵ�޸�
		nRet = _CvtAsdu10ToSgExc(Asdu10Info,lMsg);
		break;
	case EC_GROUP_SOFT:      // ��ѹ��Ͷ��
		nRet = _CvtAsdu10ToSoftExc(Asdu10Info,lMsg);
		break;
	case EC_GROUP_ZONE:      // �����л�
		nRet = _CvtAsdu10ToZoneExc(Asdu10Info,lMsg);
		break;
	case EC_GROUP_HARD:      // Ӳѹ��Ͷ��
		nRet = _CvtAsdu10ToHardExc(Asdu10Info,lMsg);
		break;
	default:
		sprintf(cError,"_CvtAsdu10InfoToRCtlExc()���յ��Ŀ�������������Ϊ:%d,��֧�ִ���",nGroupType);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
		nRet = EC_PRO_CVT_NOSUPPORT;
		break;
	}
	return nRet;
}
/**
* @brief         ����ASDU10��Ϣת��ΪNXӲѹ��Ͷ��ԤУ����
* @param[in]     ASDU10_INFO &Asdu10Info:asdu10��Ϣ�ṹ
* @param[out]    NX_COMMON_MSG_LIST& lMsg:ͨ����Ϣ�б�
* @return        int 0-�ɹ� ����ʧ��
*/
int TNXEcProAsdu10FJ::_CvtAsdu10ToHardCheck(IN ASDU10_INFO & Asdu10Info,OUT NX_COMMON_MSG_LIST& lMsg)
{
	char cError[255]="";
	NX_COMMON_MESSAGE CommonMsg;

	int nRet = __CvtAsdu10InfoStructToCommonMsg(NX_IED_CTRL_HARDSTRAP_CHECK_ASK,Asdu10Info,CommonMsg);
	if( nRet != EC_PRO_CVT_SUCCESS )
		return nRet;

	// �ж�ת�������ɵ�NX�Ӽ��Ƿ����0
	if( CommonMsg.list_subfields.size() <= 0 )
	{
		sprintf(cError,"_CvtAsdu10ToSofCheck():ת��Ӳѹ��ԤУ����(addr=%d cpu=%d)ʱ,û�����ɶ�Ӧ��Ӳѹ�����Ϣ",
			Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10");
		return EC_PRO_CVT_FAIL;
	}

	// �����ɵ�ͨ����Ϣ���ӵ�ͨ����Ϣ�б�
	lMsg.push_back(CommonMsg);

	sprintf(cError,"_CvtAsdu10ToSofCheck():Ӳѹ��ԤУ����(addr=%d cpu=%d)ת��Ϊͨ����Ϣ�ɹ�,����%d��Ӳѹ�����Ϣ",
		Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu,CommonMsg.list_subfields.size());
	RcdTrcLogWithParentClass(cError,"TNXEcProAsdu10");

	m_CsgLogDataUnit.e_log_unit_datatype = CSG_LOG_UNIT_DATATYPE_SOFTBORAD;
	m_CsgLogDataUnit.e_attribute         = CSG_LOG_UNIT_ATTRIBUTE_WRITE;
	m_CsgLogDataUnit.str_sponer    = "��վ";
	m_CsgLogDataUnit.e_behavior  = CSG_LOG_UNIT_BEHAVIOR_SETTING_SOLIDIFICATION;
	m_CsgLogDataUnit.str_target    = "��վ";
	m_CsgLogDataUnit.str_dataobject = "Ӳѹ��Ͷ��ԤУ����.";
	_ZERO_MEM(cError,255);
	sprintf(cError,"addr=%d cpu=%d",Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu);
	m_CsgLogDataUnit.str_dataauxiliary = cError;
	CsgLogOutOpr(m_CsgLogDataUnit);

	CommonMsg.list_subfields.clear();
	return EC_PRO_CVT_SUCCESS;
}
/**
* @brief         ����ASDU10��Ϣת��ΪNXӲѹ��Ͷ��ִ������
* @param[in]     ASDU10_INFO &Asdu10Info:asdu10��Ϣ�ṹ
* @param[out]    NX_COMMON_MSG_LIST& lMsg:ͨ����Ϣ�б�
* @return        int 0-�ɹ� ����ʧ��
*/
int TNXEcProAsdu10FJ::_CvtAsdu10ToHardExc(IN ASDU10_INFO & Asdu10Info,OUT NX_COMMON_MSG_LIST& lMsg)
{
	char cError[255]="";
	NX_COMMON_MESSAGE CommonMsg;

	int nRet = __CvtAsdu10InfoStructToCommonMsg(NX_IED_CTRL_HARDSTRAP_EXC_ASK,Asdu10Info,CommonMsg);
	if( nRet != EC_PRO_CVT_SUCCESS )
		return nRet;

	// �����ɵ�ͨ����Ϣ���ӵ�ͨ����Ϣ�б�
	lMsg.push_back(CommonMsg);

	sprintf(cError,"_CvtAsdu10ToSoftExc():Ӳѹ��Ͷ��ִ������(addr=%d cpu=%d)ת��Ϊͨ����Ϣ�ɹ�",
		Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu);
	RcdTrcLogWithParentClass(cError,"TNXEcProAsdu10");

	m_CsgLogDataUnit.e_log_unit_datatype = CSG_LOG_UNIT_DATATYPE_SETTING;
	m_CsgLogDataUnit.e_attribute         = CSG_LOG_UNIT_ATTRIBUTE_WRITE;
	m_CsgLogDataUnit.str_sponer    = "��վ";
	m_CsgLogDataUnit.e_behavior  = CSG_LOG_UNIT_BEHAVIOR_SETTING_SOLIDIFICATION;
	m_CsgLogDataUnit.str_target    = "��վ";
	m_CsgLogDataUnit.str_dataobject = "Ӳѹ��Ͷ��ִ������.";
	_ZERO_MEM(cError,255);
	sprintf(cError,"addr=%d cpu=%d",Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu);
	m_CsgLogDataUnit.str_dataauxiliary = cError;
	CsgLogOutOpr(m_CsgLogDataUnit);

	CommonMsg.list_subfields.clear();
	return EC_PRO_CVT_SUCCESS;
}
/*****************************************************************
* @brief         ���ݿ����������ݿ��в���ָ���豸ID.
* @param[in]     int nIed_Id:�豸ID.
* @param[out]    INT nStation_Id: ��վID.
* @return        int 0-�ɹ� ����ʧ��
****************************************************************/
int TNXEcProAsdu10FJ::__DBQueryIedByBack(IN int &nBack,OUT int & nIed,OUT int & nCpu)
{
	INXEcModelMgr * pModelMgr;
	DB_OPER_PARAM suParam;				 // ���ݿ��ѯ�����ṹ��
	DB_FIELD_DATA suField;               //�ֶνṹ��
	DB_CONDITION  suCondition;           //�����ṹ��
	CPlmRecordSet RcdSet;				 //������ݼ�

	char cDBError[255]="";
	char cLogMsg[500]="";

	//Ҫ��ѯ���������ֶ�
	suField.str_fd_name="ied_obj";			//IED
	suParam.lst_fddata.push_back(suField);
	//Ҫ��ѯ���������ֶ�
	suField.str_fd_name="ld_code";			//CPU
	suParam.lst_fddata.push_back(suField);


	//��ѯ����
	suParam.lst_tablename.push_back("nx_t_ied_hardstrap_cfg");

	//��ѯ����1  �豸ID
	suCondition.str_cdt_name="strbackup1";
	char cIed_Id[50]="";
	sprintf(cIed_Id,"%d",nBack);
	suCondition.str_cdt_value=cIed_Id;
	suCondition.e_cdt_type		=CDT_TYPE_EQUAL;
	suCondition.e_cdt_val_type  =FD_TYPE_NUMERIC;
	suParam.lst_condition.push_back(suCondition);

	//��ѯnx_t_ied_comtrade_list��ʷ¼���б���,�Ƿ���ڷ���ָ�������ļ�¼.
	pModelMgr=m_pModelSeek->GetModelMgrObj();	
	int nRet=pModelMgr->dbm_select_records(&suParam,RcdSet,false,cDBError);
	if(nRet!=0)
	{
		_clear_db_opera_param(suParam); // �����ѯ��Ϣ�б�
		sprintf(cLogMsg,"__DBQueryIedByBack():ȫվ��ѯnBackΪ%d����Ϣʱ����[%s]",nBack,cDBError);
		RcdErrLogWithParentClass(cLogMsg,"TNXEcProAsdu10FJ");
		return EC_PRO_CVT_FAIL;
	}

	_clear_db_opera_param(suParam); // �����ѯ��Ϣ�б�

	//�����
	UINT nRecordNum=0;
	RcdSet.get_record_num(nRecordNum);
	if(0==nRecordNum)//û���ҵ���Ӧ�ļ�
	{
		RcdSet.clear_result();
		memset(cLogMsg,0,500);
		sprintf(cLogMsg,"__DBQueryIedByBack():ȫվ��ied��,nBackΪ%d�����ò�����.",nBack);
		RcdErrLogWithParentClass(cLogMsg,"TNXEcProAsdu10FJ");
		return EC_PRO_CVT_FAIL;
	}
	string strValue;
	RcdSet.get_field_value_row(1,1,strValue);
	nIed = atoi(strValue.c_str());

	RcdSet.get_field_value_row(2,1,strValue);
	nCpu = atoi(strValue.c_str());

	RcdSet.clear_result();
	memset(cLogMsg,0,500);
	sprintf(cLogMsg,"__DBQueryIedByBack():��ȡnBack��%d,nIed��%d.nCpu��%d.",nBack,nIed,nCpu);
	RcdTrcLogWithParentClass(cLogMsg,"TNXEcProAsdu10FJ");
	return EC_PRO_CVT_SUCCESS;
}
/*****************************************************************
	* @brief         ���ݶ�ֵ���ƴ����ݿ��в��Ҷ�Ӧ�����Ŀ��.
	* @param[in]     string strSgName:��ֵ����.
	* @param[out]    INT nGroup: ���.
	* @param[out]    INT nEntry: ��Ŀ��.
	* @return        bool true-�ɹ� false-ʧ��
	****************************************************************/
bool TNXEcProAsdu10FJ::___GetGroupCfg(IN char *cSgName,OUT u_int8 & nGroup,OUT u_int8 & nEntry)
{
	INXEcModelMgr * pModelMgr;
	DB_OPER_PARAM suParam;				 // ���ݿ��ѯ�����ṹ��
	DB_FIELD_DATA suField;               //�ֶνṹ��
	DB_CONDITION  suCondition;           //�����ṹ��
	CPlmRecordSet RcdSet;				 //������ݼ�

	char cDBError[255]="";
	char cLogMsg[500]="";

	//Ҫ��ѯ���������ֶ�
	suField.str_fd_name="outgroup103";			//IED
	suParam.lst_fddata.push_back(suField);
	//Ҫ��ѯ���������ֶ�
	suField.str_fd_name="outitem103";			//CPU
	suParam.lst_fddata.push_back(suField);


	//��ѯ����
	suParam.lst_tablename.push_back("nx_t_ied_sg_cfg");

	//��ѯ����1  �豸ID
	suCondition.str_cdt_name="aliasname";
	char cIed_Id[50]="";
	sprintf(cIed_Id,"%s",cSgName);
	suCondition.str_cdt_value=cIed_Id;
	suCondition.e_cdt_type		=CDT_TYPE_EQUAL;
	suCondition.e_cdt_val_type  =FD_TYPE_CHAR;
	suParam.lst_condition.push_back(suCondition);

	//��ѯnx_t_ied_sg_cfg��,ָ����ֵ���Ƶ������Ŀ��.
	pModelMgr=m_pModelSeek->GetModelMgrObj();	
	int nRet=pModelMgr->dbm_select_records(&suParam,RcdSet,false,cDBError);
	if(nRet!=0)
	{
		_clear_db_opera_param(suParam); // �����ѯ��Ϣ�б�
		sprintf(cLogMsg,"___GetGroupCfg():ȫվ��ѯcSgNameΪ%s����Ϣʱ����[%s]",cSgName,cDBError);
		RcdErrLogWithParentClass(cLogMsg,"TNXEcProAsdu10FJ");
		return false;
	}

	_clear_db_opera_param(suParam); // �����ѯ��Ϣ�б�

	//�����
	UINT nRecordNum=0;
	RcdSet.get_record_num(nRecordNum);
	if(0==nRecordNum)//û���ҵ���Ӧ�ļ�
	{
		RcdSet.clear_result();
		memset(cLogMsg,0,500);
		sprintf(cLogMsg,"___GetGroupCfg():ȫվ��ied��,cSgNameΪ%s�����ò�����.",cSgName);
		RcdErrLogWithParentClass(cLogMsg,"TNXEcProAsdu10FJ");
		return false;
	}
	string strValue;
	RcdSet.get_field_value_row(1,1,strValue);
	nGroup = atoi(strValue.c_str());

	RcdSet.get_field_value_row(2,1,strValue);
	nEntry = atoi(strValue.c_str());

	RcdSet.clear_result();
	memset(cLogMsg,0,500);
	sprintf(cLogMsg,"___GetGroupCfg():��ȡcSgName��%s,nGroup��%d.nEntry��%d.",cSgName,nGroup,nEntry);
	RcdTrcLogWithParentClass(cLogMsg,"TNXEcProAsdu10FJ");
	return true;
}
/**
* @brief         ����NXӲѹ��ԤУ��Ӧ������ٻ��������ɹ�Լ����б�
* @param[in]     PRO_FRAME_BODY_LIST & lCmd:��Լ����
* @param[out]    PRO_FRAME_BODY_LIST & lResult :��Լ��Ϣ���б�
* @return        int 0-�ɹ� ����ʧ��
*/
int TNXEcProAsdu10FJ::_CvtHardCheckResultToPro(IN PRO_FRAME_BODY_LIST & lCmd,OUT PRO_FRAME_BODY_LIST & lResult)
{
	// ת��NX��ϢΪASDU10Info�ṹ
	ASDU10_INFO Asdu10Info;
	int nRet = __CvtNxSubFieldToGenList_Int(Asdu10Info.GroupToGenListMap);

	__CvtAsdu10InfoToProBody(nRet,Asdu10Info,lCmd,lResult);

	// ����ASDU10�ṹ
	ClearAsdu10Info(Asdu10Info);
	return 0;
}

/**
* @brief         ����NXӲѹ��ִ�л�Ӧ������ٻ��������ɹ�Լ����б�
* @param[in]     PRO_FRAME_BODY_LIST & lCmd:��Լ����
* @param[out]    PRO_FRAME_BODY_LIST & lResult :��Լ��Ϣ���б�
* @return        int 0-�ɹ� ����ʧ��
*/
int TNXEcProAsdu10FJ::_CvtHardExcResultToPro(IN PRO_FRAME_BODY_LIST & lCmd,OUT PRO_FRAME_BODY_LIST & lResult)
{
	// ת��NX��ϢΪASDU10Info�ṹ
	ASDU10_INFO Asdu10Info;
	int nRet = __CvtNxSubFieldToGenList_Int(Asdu10Info.GroupToGenListMap);

	__CvtAsdu10InfoToProBody(nRet,Asdu10Info,lCmd,lResult);

	// ����ASDU10�ṹ
	ClearAsdu10Info(Asdu10Info);
	return 0;
}

/**
* @brief         ����NX������Ѳ��ִ�л�Ӧ������ٻ��������ɹ�Լ����б�
* @param[in]     PRO_FRAME_BODY_LIST & lCmd:��Լ����
* @param[out]    PRO_FRAME_BODY_LIST & lResult :��Լ��Ϣ���б�
* @return        int 0-�ɹ� ����ʧ��
*/
int TNXEcProAsdu10FJ::_CvtRobertCheckResultToPro(IN PRO_FRAME_BODY_LIST & lCmd,OUT PRO_FRAME_BODY_LIST & lResult)
{
	// ת��NX��ϢΪASDU10Info�ṹ
	ASDU10_INFO Asdu10Info;
	int nRet = __CvtNxSubFieldToGenList_Int_Rot(Asdu10Info.GroupToGenListMap);

	__CvtAsdu10InfoToProBody(nRet,Asdu10Info,lCmd,lResult);

	// ����ASDU10�ṹ
	ClearAsdu10Info(Asdu10Info);
	return 0;
}
/**
* @brief         ����NX61850��ֵ��Ӧ������ٻ��������ɹ�Լ����б�
* @param[in]     PRO_FRAME_BODY_LIST & lCmd:��Լ����
* @param[out]    PRO_FRAME_BODY_LIST & lResult :��Լ��Ϣ���б�
* @return        int 0-�ɹ� ����ʧ��
*/
int TNXEcProAsdu10FJ::_Cvt61850ReadResultToPro(IN PRO_FRAME_BODY_LIST & lCmd,OUT PRO_FRAME_BODY_LIST & lResult)
{
	// ת��NX��ϢΪASDU10Info�ṹ
	ASDU10_INFO Asdu10Info;
	int nRet = __CvtNxSubFieldToGenList_Int_61850Read(Asdu10Info.GroupToGenListMap);

	__CvtAsdu10InfoToProBody(nRet,Asdu10Info,lCmd,lResult);

	// ����ASDU10�ṹ
	ClearAsdu10Info(Asdu10Info);
	return 0;
}
/**
* @brief         ����ASDU10��Ϣ�ṹ��ʽ��ASDU10������
* @param[in]     ASDU10_INFO &Asdu10Info:asdu10��Ϣ�ṹ
* @param[in]     PRO_FRAME_BODY * pCmdBody:������Ϣָ��(��ΪNULL,������֡ת���Զ�������Ϣ)
* @param[out]    PRO_FRAME_BODY_LIST  & lBody :ASDU��Լ��Ϣ���б�
* @param[in]     int nReserve:����
* @return        int 0-�ɹ� ����ʧ��
*/
int TNXEcProAsdu10FJ::FormatAsdu10Body(IN ASDU10_INFO &Asdu10Info,OUT PRO_FRAME_BODY_LIST &lBody,IN PRO_FRAME_BODY * pCmdBody,IN int nReserve)
{
	char cError[255] = "";
	int nSrcSize = lBody.size();
	int nEntryNum = 0;  // ����Ŀ��
	GROUP2GENLIST_MAP::iterator iteMap;
	ASDU_GEN_INFO_LIST * pGenList= NULL;

	if( NULL != pCmdBody )        // �����������ɽ��
	{
		// ���Ϊ����ִ�����������������
		if( ( pCmdBody->nType == 0x0A ) && (pCmdBody->nCot == 0x28) && 
			(pCmdBody->nFun == 0xfe) && (pCmdBody->nInf == 0xfa) 
			)
		{
			PRO_FRAME_BODY ProBody;
			ProBody = *pCmdBody; // ���������һ��
			lBody.push_back(ProBody);
			ProBody.vVarData.clear();
			return EC_PRO_CVT_SUCCESS;
		}

		// ���Ϊ������ִ��������½��Ե��ֵ��������������
		if( ( ( pCmdBody->nType == 0x0A ) && (pCmdBody->nCot == 0x2A) && 
			(pCmdBody->nFun == 0xfe) && (pCmdBody->nInf == 0xf2) 
			) || ( ( pCmdBody->nType == 0x0A ) && (pCmdBody->nCot == 0x2A) && 
			(pCmdBody->nFun == 0xfe) && (pCmdBody->nInf == 0xfe)) )
		{
			PRO_FRAME_BODY ProBody;
			ProBody = *pCmdBody; // ���������һ��
			lBody.push_back(ProBody);
			ProBody.vVarData.clear();
			return EC_PRO_CVT_SUCCESS;
		}

		// ���Ϊ�ٻ��������������⴦��(��������û�������Ϣ)
		if( (pCmdBody->nType == 0x15 ) && ( pCmdBody->nFun == 0xfe ) && (pCmdBody->nInf == 0xf0 ) )
		{
			if( Asdu10Info.GroupToGenListMap.size() <= 0 )
			{
				sprintf(cError,"FormatAsdu10Body():asdu10�ṹ���������Ϊ0��.(cmd:addr=%d cpu=%d),���������ʧ�ܻ�Ӧ",
					pCmdBody->nAddr,pCmdBody->nCpu);
				RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
				MakeGenericFailResultByCmd(pCmdBody,lBody); // ����ʧ�ܻ�Ӧ
				return EC_PRO_CVT_SUCCESS;
			}
			iteMap = Asdu10Info.GroupToGenListMap.begin();
			if( iteMap->second != NULL )
				nEntryNum = iteMap->second->size();
		}
		else  // �ж�ASDU10��Ϣ�ṹ���Ƿ��ж�Ӧ��������ŵĽ��
		{
			if( Asdu10Info.GroupToGenListMap.count(pCmdBody->nGroup) <= 0 )  // û�ж�Ӧ��Ľ��
			{
				sprintf(cError,"FormatAsdu10Body():asdu10�ṹ��û�����=%d�Ľ���������Ӧ(cmd:addr=%d cpu=%d zone=%d group=%d),���ɸ���ʧ�ܻ�Ӧ",
					pCmdBody->nGroup,pCmdBody->nAddr,pCmdBody->nCpu,pCmdBody->nZone,pCmdBody->nGroup);
				RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
				MakeGenericFailResultByCmd(pCmdBody,lBody); 	// ����ʧ�ܻ�Ӧ
				return EC_PRO_CVT_SUCCESS;
			}
			pGenList = Asdu10Info.GroupToGenListMap[pCmdBody->nGroup];
			if( pGenList != NULL )
				nEntryNum = pGenList->size();
		}

		// ����ASDU10��Ϣ�ṹ�Ļ�����Ϣ
		Asdu10Info.Addr.nAddr = pCmdBody->nAddr;
		Asdu10Info.Addr.nSubstationAdd = pCmdBody->nSubstationAdd;
		Asdu10Info.Addr.nCpu = pCmdBody->nCpu;
		//Asdu10Info.Addr.nZone = pCmdBody->nZone; // ����ж�ֵ���Ż�Ӧ���������һ��(�ھ���Ӧ�������ã�
		Asdu10Info.InfoObj.nFun = pCmdBody->nFun;
		Asdu10Info.InfoObj.nInf = pCmdBody->nInf;

		// ���Ϊ����ԤУ����
		if( ( pCmdBody->nType == 0x0A )&&(pCmdBody->nCot == 0x28)&&(pCmdBody->nFun == 0xfe)&&(pCmdBody->nInf == 0xf9) )
		{
			Asdu10Info.nCot = 0x2c;     // ԤУ�ɹ���ȷ�ϴ���ԭ��
		}
		else
		{
			Asdu10Info.nCot = pCmdBody->nCot;
		}
		// ��ʽ�����鱨����
		if( _FormatOneGroupAsdu10Body(Asdu10Info,pCmdBody->nGroup,pCmdBody->nRii,lBody) != EC_PRO_CVT_SUCCESS )
		{
			MakeGenericFailResultByCmd(pCmdBody,lBody); // ����ʧ�ܻ�Ӧ
			nEntryNum = 0;
		}
		sprintf(cError,"FormatAsdu10Body():Ϊcmd(addr=%d cpu=%d inf=%d group=%d rii=%d)����%d��asdu10,����Ŀ��:%d",
			pCmdBody->nAddr,pCmdBody->nCpu,pCmdBody->nInf,pCmdBody->nGroup,pCmdBody->nRii,lBody.size()-nSrcSize,nEntryNum);
	}
	else     // �Զ�����
	{
		// ��ʽ���������Զ�����
		iteMap = Asdu10Info.GroupToGenListMap.begin();
		while( iteMap != Asdu10Info.GroupToGenListMap.end() )
		{
			_FormatOneGroupAsdu10Body(Asdu10Info,iteMap->first,0,lBody);
			pGenList = iteMap->second;
			if( pGenList != NULL )
				nEntryNum += pGenList->size();
			++iteMap;
		}
		sprintf(cError,"FormatAsdu10Body():����%d���Զ�����asdu10,addr=%d cpu=%d ����Ŀ��=%d ",
			lBody.size()-nSrcSize,Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu,nEntryNum);
	}
	RcdTrcLogWithParentClass(cError,"TNXEcProAsdu10FJ");
	return 0;
}
/**
* @brief         ����ASDU10��Ϣ��������ʧ�ܻ�Ӧ
* @param[in]     PRO_FRAME_BODY * pCmdBody:������Ϣ
* @param[out]    PRO_FRAME_BODY_LIST & lResult :��Լ��Ϣ���б�
* @return        int 0-�ɹ� ����ʧ��
*/
int TNXEcProAsdu10FJ::MakeGenericFailResultByCmd(IN PRO_FRAME_BODY * pCmdBody,OUT PRO_FRAME_BODY_LIST & lResult)
{
	// ������������ʧ�ܻ�Ӧ(���ж�����Ϊasdu21�����������asdu10)
	if( pCmdBody == NULL )
		return EC_PRO_CVT_FAIL;

	// ��ͨ�������ṹ
	PRO_FRAME_BODY ProBody(*pCmdBody);

	// �޸� ASDU����
	ProBody.nType = 0x0A;

	// �޸�����
	if( pCmdBody->nType == 0x0A) // asdu10 (��������)
	{
		if ((pCmdBody->nFun == 0xfe) && (pCmdBody->nInf == 0xf2))
		{
			// �޸Ĵ���ԭ��
			ProBody.nCot = 0x2b;

			// ����֡
			ProBody.bLast = true;
			// �����Ϊ�ٻ�����⣬������ͨ�÷�����Ч��Ӧ����
			if( pCmdBody->nInf != 0xf0 )
			{
				ProBody.vVarData.push_back(GDD_DATA_TYPE_GRC);  // ��������-ͨ�÷���ش���
				ProBody.vVarData.push_back(0x01);               // ���ݿ���
				ProBody.vVarData.push_back(0x01);               // ������Ŀ
				ProBody.vVarData.push_back(0x02);               // GRC 2-���������������
			}
		} 
		else
		{
			// �޸Ĵ���ԭ��
			ProBody.nCot = 0x29;    // ���Ͽ�
		}	
	}
	else
	{
		// �޸Ĵ���ԭ��
		ProBody.nCot = 0x2b;

		// ����֡
		ProBody.bLast = true;
		// �����Ϊ�ٻ�����⣬������ͨ�÷�����Ч��Ӧ����
		if( pCmdBody->nInf != 0xf0 )
		{
			ProBody.vVarData.push_back(GDD_DATA_TYPE_GRC);  // ��������-ͨ�÷���ش���
			ProBody.vVarData.push_back(0x01);               // ���ݿ���
			ProBody.vVarData.push_back(0x01);               // ������Ŀ
			ProBody.vVarData.push_back(0x02);               // GRC 2-���������������
		}
	}

	lResult.push_back(ProBody);

	// ��չ�Լ��
	ProBody.vVarData.clear();
	return 0;
}
/**
* @brief         ����NX�¼���Ϣ���ɹ�Լ�¼��б�
* @param[in]     NX_EVENT_MESSAGE * pMsg :�¼��Žṹָ��
* @param[out]    PRO_FRAME_BODY_LIST  & lBody :��Լ��Ϣ��
* @return        int 0-�ɹ� ����ʧ��
*/
int TNXEcProAsdu10FJ::ConvertEventMsgToPro(IN NX_EVENT_MESSAGE* pMsg,OUT PRO_FRAME_BODY_LIST & lBody)
{
	char cError[255] = "";
	int  nRet = EC_PRO_CVT_FAIL ;

	m_pEventMsg = pMsg;

	switch(pMsg->n_msg_type)
	{
	case NX_IED_EVENT_SOFTTRAP_REPORT:         // ��ѹ��
		nRet = _CvtSoftReportToPro(lBody);
		break;
	case NX_SYS_EVENT_IED_SGZONE_CHG_REPORT:   // ��ֵ���仯
		nRet = _CvtZoneChgReportToPro(lBody);
		break;
	case NX_IED_CALL_ROBOTCHECK_REPORT:   // ������Ѳ�ӱ���
		nRet = _CvtRobertCheckReportToPro(lBody);
		break;
	default:
		sprintf(cError,"ConvertEventMsgToPro()���ݲ�֧��n_msg_type=%d����Ϣ����",pMsg->n_msg_type);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
		nRet = EC_PRO_CVT_NOSUPPORT;
		break;
	}
	m_pEventMsg = NULL;
	return nRet;
}
/**
* @brief         ת����Լ��Ϣ��NXͨ����Ϣ�ṹ
* @param[in]     PRO_FRAME_BODY_LIST* pBodyList :��Լ��Ϣ���б�ָ��
* @param[out]    NX_COMMON_MSG_LIST & lMsg: �������ɵ�ͨ����Ϣ�б�
* @param[out]    PRO_FRAME_BODY_LIST & lResult���������ɵĹ�Լʧ�ܻ�Ӧ(����˹�Լ��Ч��
* @return        int 0-�ɹ� ����ʧ��
*/
int TNXEcProAsdu10FJ::ConvertProToCommonMsg(IN PRO_FRAME_BODY_LIST* pBodyList,OUT NX_COMMON_MSG_LIST & lMsg,OUT PRO_FRAME_BODY_LIST & lResult)
{
	char cError[255] = "";
	int  nRet = EC_PRO_CVT_FAIL ;            // ����ֵ
	int  nGropTitleType = 0;                 //���������
	ASDU10_INFO  Asdu10Info;

	if( pBodyList == NULL )
		return nRet;
	string striii;

	// ����Լ����ת��ΪASDU10��Ϣ�ṹ
	if( ProBodyToAsdu10Struct(*pBodyList,Asdu10Info) != EC_PRO_CVT_SUCCESS )
	{
		_MakeRCtlFailResultByCmd(*pBodyList,lResult);
		ClearAsdu10Info(Asdu10Info);
		return nRet;
	}
	// 	ȷ��ͨ�÷���������һ��
	// 		if( Asdu10Info.GroupToGenListMap.size() <= 0 )
	// 		{
	// 			sprintf(cError,"�յ�ASDU10(addr=%d fun=%d inf=%d cot =%d)û������Ϣ,����ʧ��",
	// 				    Asdu10Info.Addr.nAddr,Asdu10Info.InfoObj.nFun,Asdu10Info.InfoObj.nInf,Asdu10Info.nCot);
	// 			RcdErrLogWithParentClass(cError,"TNXEcProAsdu10");
	// 			_MakeRCtlFailResultByCmd(*pBodyList,lResult);
	// 			ClearAsdu10Info(Asdu10Info);
	// 			return nRet;
	// 		}
	// 		// ����ͨ�÷�����Ż��������(ȡͷһ���жϼ��ɣ�ͬһ�������еĶ��鶼Ϊͬһ����)
	// 		GROUP2GENLIST_MAP::iterator ite = Asdu10Info.GroupToGenListMap.begin();
	nGropTitleType = _GetGroupTitleType(Asdu10Info.Addr.nSubstationAdd,Asdu10Info.Addr.nAddr,
		Asdu10Info.Addr.nCpu,Asdu10Info.nGroup);

	// ����ͨ�÷�����Ϣ������������
	if( ( Asdu10Info.nCot == 0x28) && (Asdu10Info.InfoObj.nFun == 0xfe) )  // ��������
	{
		if( Asdu10Info.InfoObj.nInf == 0xf9 ) // ԤУ
		{
			nRet = _CvtAsdu10InfoToRCtlCheck(nGropTitleType,Asdu10Info,lMsg);
		}
		else if ( Asdu10Info.InfoObj.nInf == 0xfa) // ִ��
		{
			nRet = _CvtAsdu10InfoToRCtlExc(nGropTitleType,Asdu10Info,lMsg);
		}
		else
		{
			//����
			sprintf(cError,"ConvertProToCommonMsg()���յ��Ŀ�������nInf=%d,���󲻴���",Asdu10Info.InfoObj.nInf);
			RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
		}
	}
	else if (( Asdu10Info.nCot == 0x2a) && (Asdu10Info.InfoObj.nFun == 0xfe) && (Asdu10Info.InfoObj.nInf == 0xf2))//������Ѳ������ update by yys
	{
		nRet = _CvtRobotProToNX(Asdu10Info,lMsg);
	}
	else
	{
		//���������ݲ�֧�ִ���
		sprintf(cError,"ConvertProToCommonMsg()�в�֧��cot=%d��Fun=%d��Inf=%d����Ϣ����",
			Asdu10Info.nCot,Asdu10Info.InfoObj.nFun,Asdu10Info.InfoObj.nInf);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
		nRet = EC_PRO_CVT_NOSUPPORT;
	}

	// ת��ʧ�ܻ�֧�ֵĴ���
	if( nRet != EC_PRO_CVT_SUCCESS ) 
	{
		_MakeRCtlFailResultByCmd(*pBodyList,lResult);
	}

	// ����ASDU10�ṹ
	ClearAsdu10Info(Asdu10Info);

	return nRet;
}
/**
* @brief         ����ASDU10��Ϣת��ΪNX��ѹ��Ͷ��ԤУ����
* @param[in]     ASDU10_INFO &Asdu10Info:asdu10��Ϣ�ṹ
* @param[out]    NX_COMMON_MSG_LIST& lMsg:ͨ����Ϣ�б�
* @return        int 0-�ɹ� ����ʧ��
*/
int TNXEcProAsdu10FJ::_CvtRobotProToNX(IN ASDU10_INFO & Asdu10Info,OUT NX_COMMON_MSG_LIST& lMsg)
{
	char cError[255]="";
	NX_COMMON_MESSAGE CommonMsg;

	int nRet = __CvtRotInfoToCommonMsg(Asdu10Info,CommonMsg);
	if( nRet != EC_PRO_CVT_SUCCESS )
		return nRet;

	// �ж�ת�������ɵ�NX�Ӽ��Ƿ����0
	if( CommonMsg.list_subfields.size() <= 0 )
	{
		sprintf(cError,"_CvtRobotProToNX():������Ѳ������(addr=%d cpu=%d)ʱ,û�����ɶ�Ӧ��Ѳ�ӵ���Ϣ",
			Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
		return EC_PRO_CVT_FAIL;
	}

	// �����ɵ�ͨ����Ϣ���ӵ�ͨ����Ϣ�б�
	lMsg.push_back(CommonMsg);

	sprintf(cError,"_CvtRobotProToNX():������Ѳ������(addr=%d cpu=%d)ת��Ϊͨ����Ϣ�ɹ�,����%d��Ѳ�ӵ���Ϣ",
		Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu,CommonMsg.list_subfields.size());
	RcdTrcLogWithParentClass(cError,"TNXEcProAsdu10FJ");

	m_CsgLogDataUnit.e_log_unit_datatype = CSG_LOG_UNIT_DATATYPE_OTHER;
	m_CsgLogDataUnit.e_attribute         = CSG_LOG_UNIT_ATTRIBUTE_READ;
	m_CsgLogDataUnit.str_sponer    = "��վ";
	m_CsgLogDataUnit.e_behavior  = CSG_LOG_UNIT_BEHAVIOR_DATA_RCV_ANALYZE;
	m_CsgLogDataUnit.str_target    = "��վ";
	m_CsgLogDataUnit.str_dataobject = "������Ѳ������.";
	_ZERO_MEM(cError,255);
	sprintf(cError,"addr=%d cpu=%d",Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu);
	m_CsgLogDataUnit.str_dataauxiliary = cError;
	CsgLogOutOpr(m_CsgLogDataUnit);

	CommonMsg.list_subfields.clear();
	return EC_PRO_CVT_SUCCESS;
}
/**
* @brief         ����ASDU10��Ϣת��ΪNX61850��ֵ����
* @param[in]     ASDU10_INFO &Asdu10Info:asdu10��Ϣ�ṹ
* @param[out]    NX_COMMON_MSG_LIST& lMsg:ͨ����Ϣ�б�
* @return        int 0-�ɹ� ����ʧ��
*/
int TNXEcProAsdu10FJ::_CvtProToNX61850Read(IN ASDU10_INFO & Asdu10Info,OUT NX_COMMON_MSG_LIST& lMsg)
{
	char cError[255]="";
	NX_COMMON_MESSAGE CommonMsg;

	int nRet = __CvtReadPointInfoToCommonMsg(Asdu10Info,CommonMsg);
	if( nRet != EC_PRO_CVT_SUCCESS )
		return nRet;

	// �ж�ת�������ɵ�NX�Ӽ��Ƿ����0
	if( CommonMsg.list_subfields.size() <= 0 )
	{
		sprintf(cError,"_CvtProToNX61850Read():������Ѳ������(addr=%d cpu=%d)ʱ,û�����ɶ�Ӧ��Ѳ�ӵ���Ϣ",
			Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
		return EC_PRO_CVT_FAIL;
	}

	// �����ɵ�ͨ����Ϣ���ӵ�ͨ����Ϣ�б�
	lMsg.push_back(CommonMsg);

	sprintf(cError,"_CvtProToNX61850Read():61850�����ֵ(addr=%d cpu=%d)ת��Ϊͨ����Ϣ�ɹ�.",
		Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu);
	RcdTrcLogWithParentClass(cError,"TNXEcProAsdu10FJ");

	m_CsgLogDataUnit.e_log_unit_datatype = CSG_LOG_UNIT_DATATYPE_OTHER;
	m_CsgLogDataUnit.e_attribute         = CSG_LOG_UNIT_ATTRIBUTE_READ;
	m_CsgLogDataUnit.str_sponer    = "��վ";
	m_CsgLogDataUnit.e_behavior  = CSG_LOG_UNIT_BEHAVIOR_DATA_RCV_ANALYZE;
	m_CsgLogDataUnit.str_target    = "��վ";
	m_CsgLogDataUnit.str_dataobject = "61850�����ֵ";
	_ZERO_MEM(cError,255);
	sprintf(cError,"addr=%d cpu=%d",Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu);
	m_CsgLogDataUnit.str_dataauxiliary = cError;
	CsgLogOutOpr(m_CsgLogDataUnit);

	CommonMsg.list_subfields.clear();
	return EC_PRO_CVT_SUCCESS;
}
/**
* @brief         ת��ΪNX������Ѳ������
* @param[in]     ASDU10_INFO &Asdu10Info:asdu10��Ϣ�ṹ
* @param[out]    NX_COMMON_MSG_LIST& lMsg:ͨ����Ϣ�б�
* @return        int 0-�ɹ� ����ʧ��
*/
int TNXEcProAsdu10FJ::__CvtRotInfoToCommonMsg(IN ASDU10_INFO & Asdu10Info,OUT NX_COMMON_MESSAGE &CommonMsg)
{
	char cError[255]="";
	// ��õ�ַ��Ϣ
	const IED_TB * pIedTb = m_pModelSeek->GetIedBasicCfgByAddr103(Asdu10Info.Addr.nAddr);
	if( pIedTb == NULL )
	{
		return EC_PRO_CVT_FAIL;
	}

	CommonMsg.n_msg_topic = NX_TOPIC_COMMAND;
	CommonMsg.n_msg_type  = NX_IED_CALL_ROBOTCHECK_ASK;
	CommonMsg.n_obj_id    = pIedTb->n_obj_id;
	CommonMsg.n_obj_type  = NX_OBJ_TYPE_NX_IED;
	CommonMsg.n_sub_obj_id= Asdu10Info.Addr.nCpu;
	CommonMsg.n_sub_sub_obj_id = Asdu10Info.Addr.nZone;
	CommonMsg.n_data_src = 2;  // ��ʾ��������Զ����վ
	CommonMsg.b_lastmsg  = true;

	// ת��NX_SUBFIELDS
	NX_COMMON_MSG_SUBFILED_LIST * pNxSubList = &CommonMsg.list_subfields;
	GROUP2GENLIST_MAP::iterator iteMap = Asdu10Info.GroupToGenListMap.begin();
	ASDU_GEN_INFO_LIST * pGenList = NULL;
	ASDU_GEN_INFO_LIST::iterator ite;
	NX_COMMON_FIELD_STRUCT tmpSubField;
	string strValue;
	int nID = 0;

	while( iteMap != Asdu10Info.GroupToGenListMap.end() )
	{
		pGenList = iteMap->second;
		if( pGenList == NULL )
		{
			++iteMap;
			continue;
		}
		// ����ͨ�÷������ݼ��б�
		ite = pGenList->begin();
		while (ite != pGenList->end())
		{
			tmpSubField.n_field_id = -1;
			_ZERO_MEM(tmpSubField.c_value,sizeof(tmpSubField.c_value));
			tmpSubField.n_value = -1;
			strValue.clear();
			nID = 0;

			// ��ͨ�÷�����������ת��Ϊ�ַ�ֵ
			strValue = ___CvtGenericValueToStr(*ite);
			printf("--------------------------------->%s\n",strValue.c_str());

			memset(tmpSubField.c_field_name,0,strlen(strValue.c_str()));
			memcpy(tmpSubField.c_field_name,strValue.c_str(),strlen(strValue.c_str()));
			//memcpy(tmpSubField.c_field_name,strValue.c_str(),_MIN_VAL(strValue.size(),sizeof(tmpSubField.c_value)-1));

			sprintf(cError,"_CvtProToNX61850Read():�������·���Ѳ���豸����Ϊ��%s.",
				tmpSubField.c_field_name);
			RcdTrcLogWithParentClass(cError,"TNXEcProAsdu10FJ");
			// ����NX�Ӽ��б�
			pNxSubList->push_back(tmpSubField);

			++ite;
		} 

		pGenList = NULL;
		++iteMap;
	} 

	return EC_PRO_CVT_SUCCESS;
}
/**
* @brief         ת��ΪNX61850��ְ
* @param[in]     ASDU10_INFO &Asdu10Info:asdu10��Ϣ�ṹ
* @param[out]    NX_COMMON_MSG_LIST& lMsg:ͨ����Ϣ�б�
* @return        int 0-�ɹ� ����ʧ��
*/
int TNXEcProAsdu10FJ::__CvtReadPointInfoToCommonMsg(IN ASDU10_INFO & Asdu10Info,OUT NX_COMMON_MESSAGE &CommonMsg)
{
	// ��õ�ַ��Ϣ
	char cError[255]="";
	const IED_TB * pIedTb = m_pModelSeek->GetIedBasicCfgByAddr103(Asdu10Info.Addr.nAddr);
	if( pIedTb == NULL )
	{
		return EC_PRO_CVT_FAIL;
	}

	CommonMsg.n_msg_topic = NX_TOPIC_COMMAND;
	CommonMsg.n_msg_type  = NX_IED_CALL_61850SRV_READ_ASK;
	CommonMsg.n_obj_id    = pIedTb->n_obj_id;
	CommonMsg.n_obj_type  = NX_OBJ_TYPE_NX_IED;
	CommonMsg.n_sub_obj_id= Asdu10Info.Addr.nCpu;
	CommonMsg.n_sub_sub_obj_id = Asdu10Info.Addr.nZone;
	sprintf(CommonMsg.c_obj_pathname,"%s",pIedTb->str_objpathname.c_str());
	CommonMsg.n_data_src = 2;  // ��ʾ��������Զ����վ
	CommonMsg.b_lastmsg  = true;

	// ת��NX_SUBFIELDS
	NX_COMMON_MSG_SUBFILED_LIST * pNxSubList = &CommonMsg.list_subfields;
	GROUP2GENLIST_MAP::iterator iteMap = Asdu10Info.GroupToGenListMap.begin();
	ASDU_GEN_INFO_LIST * pGenList = NULL;
	ASDU_GEN_INFO_LIST::iterator ite;
	NX_COMMON_FIELD_STRUCT tmpSubField;
	string strValue;
	int nID = 0;

	while( iteMap != Asdu10Info.GroupToGenListMap.end() )
	{
		pGenList = iteMap->second;
		if( pGenList == NULL )
		{
			++iteMap;
			continue;
		}
		// ����ͨ�÷������ݼ��б�
		ite = pGenList->begin();
		while (ite != pGenList->end())
		{
			tmpSubField.n_field_id = -1;
			_ZERO_MEM(tmpSubField.c_value,sizeof(tmpSubField.c_value));
			tmpSubField.n_value = -1;
			strValue.clear();

			const SG_TB* pSgCfg = m_pModelSeek->GetIedSgCfg(pIedTb->n_obj_id,Asdu10Info.Addr.nCpu,ite->nGroup,ite->nEntry);
			if (pSgCfg == NULL)
			{
				sprintf(cError,"__CvtReadPointInfoToCommonMsg():[GetIedSgCfg]��ȡ��ֵ����ʧ�ܣ�����",
					Asdu10Info.Addr.nAddr,Asdu10Info.Addr.nCpu);
				RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
				continue;
			}

			sprintf(tmpSubField.c_field_name,"%s",pSgCfg->str_objpathname.c_str());

			// ����NX�Ӽ��б�
			pNxSubList->push_back(tmpSubField);

			++ite;
		} 

		pGenList = NULL;
		++iteMap;
	} 

	return EC_PRO_CVT_SUCCESS;
}
/**
* @brief         ת��NXͨ����Ϣ�Ӽ����ݣ�ֵΪ���ͣ�Ϊ�����ͨ�÷������ݼ�ӳ���
* @param[out]     OUT GROUP2GENLIST_MAP & GToGenListMap:ͨ�÷������ݼ�ӳ���
* @return        int 0-�ɹ� ����ʧ��
*/
//int TNXEcProAsdu10FJ::__CvtNxSubFieldToGenList_Int(OUT GROUP2GENLIST_MAP & GToGenListMap)
//{
//	char cError[255]="";
//	ASDU_GEN_INFO GenData;     // ͨ�÷������ݽṹ
//	ASDU_GEN_INFO_LIST * pGenList = NULL;
//	u_int8 nGroup = 0,nEntry = 0,nDataType=0,nKod=0;
//	bool  bGetCfg = false,bDpi = false;
//	int nValue = 0;
//
//	// �жϻ�Ӧ���ܽ��
//	if(m_pCommonMsg->n_result != 0 )   // ʧ��
//		return EC_PRO_CVT_FAIL;
//
//	// ת��NX�¼��Ӽ��ṹΪͨ�÷�������
//	NX_COMMON_MSG_SUBFILED_LIST::iterator ite = m_pCommonMsg->list_subfields.begin();
//	while ( ite != m_pCommonMsg->list_subfields.end() )
//	{
//		GenData.vGid.clear();
//		// ������\��Ŀ�ż�����������Ϣ
//		if( ( m_pCommonMsg->n_msg_type == NX_IED_CALL_SGZONE_REP) || 
//			( m_pCommonMsg->n_msg_type == NX_IED_CTRL_SGZONE_CHECK_REP) ||
//			( m_pCommonMsg->n_msg_type == NX_IED_CTRL_SGZONE_EXC_REP) 
//			)
//		{
//			bGetCfg = ___GetZoneDataPoint103Cfg(ite->n_field_id,nGroup,nEntry,nDataType);
//			nValue = ite->n_value;
//		}
//		else if( ( m_pCommonMsg->n_msg_type == NX_IED_CALL_SOFTSTRAP_REP) ||
//			( m_pCommonMsg->n_msg_type == NX_IED_CTRL_SOFTSTRAP_CHECK_REP) || 
//			( m_pCommonMsg->n_msg_type == NX_IED_CTRL_SOFTSTRAP_EXC_REP)
//			)
//		{
//			bDpi = true;
//			bGetCfg = ___GetSoftDataPoint103Cfg(ite->n_field_id,nGroup,nEntry,nDataType);
//			if( ite->n_value == 1 )
//				nValue = 2;
//			else
//				nValue = 1;
//		}
//		else if( ( m_pCommonMsg->n_msg_type == NX_IED_CALL_HARDSTRAP_REP) ||
//			( m_pCommonMsg->n_msg_type == NX_IED_CTRL_HARDSTRAP_CHECK_REP) || 
//			( m_pCommonMsg->n_msg_type == NX_IED_CTRL_HARDSTRAP_EXC_REP)
//			)
//		{
//			bDpi = true;
//			bGetCfg = ___GetHardDataPoint103Cfg(ite->n_field_id,nGroup,nEntry,nDataType);
//			if( ite->n_value == 1 )
//				nValue = 2;
//			else
//				nValue = 1;
//		}
//		else
//		{
//			sprintf(cError,"__CvtNxSubFieldToGenList_Int():��֧�ֻ�ȡn_msg_type=%d��Ϣ��103������",
//				m_pCommonMsg->n_msg_type);
//			RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
//		}
//
//		if( !bGetCfg)
//		{
//			++ite;
//			continue;
//		}
//
//		nKod = ___GetGenKod(m_pCommonMsg->n_data_src);
//
//		__SetGenericIntData(nGroup,nEntry,nKod,nValue,GenData,bDpi);
//
//		// ��ͨ�÷������ݼ����б�
//		if( GToGenListMap.count(nGroup) > 0 )    // �����Ѵ������ȡ��Ӧ���б�ָ��
//		{
//			pGenList = GToGenListMap[nGroup];
//		}
//		else      // ���������½�
//		{
//			pGenList = new ASDU_GEN_INFO_LIST;
//			GToGenListMap[nGroup] = pGenList;
//		}
//
//		if( pGenList != NULL )
//			pGenList->push_back(GenData);
//
//		++ite;
//		pGenList = NULL;
//		nGroup = nEntry = nKod = nDataType = 0;
//		bDpi  = false;
//		bGetCfg = false;
//		nValue = 0;
//	}
//
//	GenData.vGid.clear();
//	return EC_PRO_CVT_SUCCESS;
//}
/**
* @brief         ת��NXͨ����Ϣ�Ӽ����ݣ�ֵΪ���ͣ�Ϊ�����ͨ�÷������ݼ�ӳ���
* @param[out]     OUT GROUP2GENLIST_MAP & GToGenListMap:ͨ�÷������ݼ�ӳ���
* @return        int 0-�ɹ� ����ʧ��
*/
int TNXEcProAsdu10FJ::__CvtNxSubFieldToGenList_Int_Rot(OUT GROUP2GENLIST_MAP & GToGenListMap)
{
	char cError[255]="";
	ASDU_GEN_INFO GenData;     // ͨ�÷������ݽṹ
	ASDU_GEN_INFO_LIST * pGenList = NULL;
	u_int8 nGroup = 0,nEntry = 0,nDataType=0,nKod=0;
	bool  bGetCfg = false,bDpi = false;
	int nValue = 0;

	// �жϻ�Ӧ���ܽ��
// 	if(m_pCommonMsg->n_result != 0 )   // ʧ��
// 		return EC_PRO_CVT_FAIL;


	GenData.vGid.clear();
	// ������\��Ŀ�ż�����������Ϣ
	if(  m_pCommonMsg->n_msg_type == NX_IED_CALL_ROBOTCHECK_REP) 		
	{
		bGetCfg = __DBQuerySgGroup(m_pCommonMsg->n_obj_id,nGroup,nEntry);
		nValue = 0;
	}
	else
	{
		sprintf(cError,"__CvtNxSubFieldToGenList_Int():��֧�ֻ�ȡn_msg_type=%d��Ϣ��103������",
			m_pCommonMsg->n_msg_type);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
	}


	nKod = 1;

	__SetGenericIntData(nGroup,nEntry,nKod,nValue,GenData,bDpi);

	// ��ͨ�÷������ݼ����б�
	if( GToGenListMap.count(nGroup) > 0 )    // �����Ѵ������ȡ��Ӧ���б�ָ��
	{
		pGenList = GToGenListMap[nGroup];
	}
	else      // ���������½�
	{
		pGenList = new ASDU_GEN_INFO_LIST;
		GToGenListMap[nGroup] = pGenList;
	}

	if ( m_pCommonMsg->n_result == 0 )//�ɹ�
	{
		GenData.nKod = 1;
	} 
	else
	{
		GenData.nKod = 0;
	}

	if( pGenList != NULL )
		pGenList->push_back(GenData);


	pGenList = NULL;
	nGroup = nEntry = nKod = nDataType = 0;
	bDpi  = false;
	bGetCfg = false;
	nValue = 0;


	GenData.vGid.clear();
	return EC_PRO_CVT_SUCCESS;
}
/**
* @brief         61850��ֵת��NXͨ����Ϣ�Ӽ����ݣ�ֵΪ���ͣ�Ϊ�����ͨ�÷������ݼ�ӳ���
* @param[out]     OUT GROUP2GENLIST_MAP & GToGenListMap:ͨ�÷������ݼ�ӳ���
* @return        int 0-�ɹ� ����ʧ��
*/
int TNXEcProAsdu10FJ::__CvtNxSubFieldToGenList_Int_61850Read(OUT GROUP2GENLIST_MAP & GToGenListMap)
{
	char cError[255]="";
	ASDU_GEN_INFO GenData;     // ͨ�÷������ݽṹ
	ASDU_GEN_INFO_LIST * pGenList = NULL;
	u_int8 nGroup = 0,nEntry = 0,nDataType=0,nKod=0;
	bool  bGetCfg = false,bDpi = false;
	int nValue = 0;

	GenData.vGid.clear();

	__SetGenericIntData(nGroup,nEntry,nKod,nValue,GenData,bDpi);

	// ��ͨ�÷������ݼ����б�
	if( GToGenListMap.count(nGroup) > 0 )    // �����Ѵ������ȡ��Ӧ���б�ָ��
	{
		pGenList = GToGenListMap[nGroup];
	}
	else      // ���������½�
	{
		pGenList = new ASDU_GEN_INFO_LIST;
		GToGenListMap[nGroup] = pGenList;
	}

	if( pGenList != NULL )
		pGenList->push_back(GenData);


	pGenList = NULL;
	nGroup = nEntry = nKod = nDataType = 0;
	bDpi  = false;
	bGetCfg = false;
	nValue = 0;


	GenData.vGid.clear();
	return EC_PRO_CVT_SUCCESS;
}
/**
* @brief         ���Ӳѹ����Ϣ���103����
* @param[in]     nGroup:��Ϣ����
* @param[out]    nGroup:���
* @param[out]    nItem: ��Ŀ��
* @param[out]    nDataType:��������
* @return        bool:true-�ɹ� false-ʧ��
*/
bool TNXEcProAsdu10FJ::___GetHardDataPoint103Cfg(IN int nID,OUT u_int8 &nGroup,OUT u_int8 &nEntry,OUT u_int8 &nDataType)
{
	char cError[255] = "";
	int nReal;
	if (0 !=__DBQueryRealIed(m_pCommonMsg->n_obj_id,m_pCommonMsg->n_sub_obj_id,nReal))
	{
		sprintf(cError,"___GetHardDataPoint103Cfg():��ȡ�豸[IED=%d,CPU=%d] ����ʵIEDʧ��.",m_pCommonMsg->n_obj_id,m_pCommonMsg->n_sub_obj_id);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
		return false;
	}

	int nRealId = nReal/1000;
	int nRealCpu= nReal%1000;

	const STRAP_TB * pTb = NULL;
	pTb = m_pModelSeek->GetIedDiCfg(nRealId,nRealCpu,nID);
	if( pTb == NULL )
	{
		sprintf(cError,"___GetHardDataPoint103Cfg():��ȡIedId=%d,LD=%d,ID=%d������ʧ��",
			m_pCommonMsg->n_obj_id,m_pCommonMsg->n_sub_obj_id,nID);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
		return false;
	}
	nGroup = pTb->n_outgroup103;
	nEntry = pTb->n_outitem103;
	nDataType = VALUE_INT;
	return true;
}
/*****************************************************************
* @brief         �����ݿ��в���ָ����վID.
* @param[in]     int nIed_Id:�豸ID.
* @param[out]    INT nStation_Id: ��վID.
* @return        int 0-�ɹ� ����ʧ��
****************************************************************/
int TNXEcProAsdu10FJ::__DBQueryRealIed(IN int nIed_Id,IN int nld_code,OUT int & nReal)
{
	INXEcModelMgr * pModelMgr;
	DB_OPER_PARAM suParam;				 // ���ݿ��ѯ�����ṹ��
	DB_FIELD_DATA suField;               //�ֶνṹ��
	DB_CONDITION  suCondition;           //�����ṹ��
	CPlmRecordSet RcdSet;				 //������ݼ�

	char cDBError[255]="";
	char cLogMsg[500]="";

	//Ҫ��ѯ���������ֶ�
	suField.str_fd_name="strbackup1";			//��ʵID
	suParam.lst_fddata.push_back(suField);


	//��ѯ����
	suParam.lst_tablename.push_back("nx_t_ied_hardstrap_cfg");

	//��ѯ����1  �豸ID
	suCondition.str_cdt_name="obj_id";
	char cIed_Id[50]="";
	sprintf(cIed_Id,"%d",nIed_Id);
	suCondition.str_cdt_value=cIed_Id;
	suCondition.e_cdt_type		=CDT_TYPE_EQUAL;
	suCondition.e_cdt_val_type  =FD_TYPE_NUMERIC;
	suParam.lst_condition.push_back(suCondition);

	//��ѯ����1  �豸ID
	suCondition.str_cdt_name="ld_code";
	sprintf(cIed_Id,"%d",nld_code);
	suCondition.str_cdt_value=cIed_Id;
	suCondition.e_cdt_type		=CDT_TYPE_EQUAL;
	suCondition.e_cdt_val_type  =FD_TYPE_NUMERIC;
	suParam.lst_condition.push_back(suCondition);

	//��ѯnx_t_ied_comtrade_list��ʷ¼���б���,�Ƿ���ڷ���ָ�������ļ�¼.
	pModelMgr=m_pModelSeek->GetModelMgrObj();	
	int nRet=pModelMgr->dbm_select_records(&suParam,RcdSet,false,cDBError);
	if(nRet!=0)
	{
		_clear_db_opera_param(suParam); // �����ѯ��Ϣ�б�
		sprintf(cLogMsg,"__DBQueryRealIed():ȫվ��ѯiedΪ%d��ld_codeΪ%d����Ϣʱ����[%s]",nIed_Id,nld_code,cDBError);
		RcdErrLogWithParentClass(cLogMsg,"TNXEcProAsdu21FJ");
		return EC_PRO_CVT_FAIL;
	}

	_clear_db_opera_param(suParam); // �����ѯ��Ϣ�б�

	//�����
	UINT nRecordNum=0;
	RcdSet.get_record_num(nRecordNum);
	if(0==nRecordNum)//û���ҵ���Ӧ�ļ�
	{
		RcdSet.clear_result();
		memset(cLogMsg,0,500);
		sprintf(cLogMsg,"__DBQueryRealIed():ȫվ��ied��,iedΪ%d,ld_codeΪ%d�����ò�����.",nIed_Id,nld_code);
		RcdErrLogWithParentClass(cLogMsg,"TNXEcProAsdu21FJ");
		return EC_PRO_CVT_FAIL;
	}
	string strValue;
	RcdSet.get_field_value_row(1,1,strValue);
	nReal = atoi(strValue.c_str());

	RcdSet.clear_result();
	memset(cLogMsg,0,500);
	sprintf(cLogMsg,"__DBQueryRealIed():��ȡIED��%d,��ʵID��%d.",nIed_Id,nReal);
	RcdTrcLogWithParentClass(cLogMsg,"TNXEcProAsdu21FJ");
	return EC_PRO_CVT_SUCCESS;
}

/*****************************************************************
* @brief         �����ݿ��в���ָ����վID.
* @param[in]     int nIed_Id:�豸ID.
* @param[out]    INT nStation_Id: ��վID.
* @return        int 0-�ɹ� ����ʧ��
****************************************************************/
int TNXEcProAsdu10FJ::__DBQuerySgGroup(IN int nIed_Id,OUT u_int8 &nGroup,OUT u_int8 &nEntry)
{
	INXEcModelMgr * pModelMgr;
	DB_OPER_PARAM suParam;				 // ���ݿ��ѯ�����ṹ��
	DB_FIELD_DATA suField;               //�ֶνṹ��
	DB_CONDITION  suCondition;           //�����ṹ��
	CPlmRecordSet RcdSet;				 //������ݼ�

	char cDBError[255]="";
	char cLogMsg[500]="";

	//Ҫ��ѯ���������ֶ�
	suField.str_fd_name="outgroup103";			//��ʵID
	suParam.lst_fddata.push_back(suField);

	suField.str_fd_name="outitem103";			//��ʵID
	suParam.lst_fddata.push_back(suField);

	//��ѯ����
	suParam.lst_tablename.push_back("nx_t_ied_sg_cfg");

	//��ѯ����1  �豸ID
	suCondition.str_cdt_name="ied_obj";
	char cIed_Id[50]="";
	sprintf(cIed_Id,"%d",nIed_Id);
	suCondition.str_cdt_value=cIed_Id;
	suCondition.e_cdt_type		=CDT_TYPE_EQUAL;
	suCondition.e_cdt_val_type  =FD_TYPE_NUMERIC;
	suParam.lst_condition.push_back(suCondition);

// 	//��ѯ����1  �豸ID
// 	suCondition.str_cdt_name="ld_code";
// 	sprintf(cIed_Id,"%d",nld_code);
// 	suCondition.str_cdt_value=cIed_Id;
// 	suCondition.e_cdt_type		=CDT_TYPE_EQUAL;
// 	suCondition.e_cdt_val_type  =FD_TYPE_NUMERIC;
// 	suParam.lst_condition.push_back(suCondition);

	//��ѯnx_t_ied_comtrade_list��ʷ¼���б���,�Ƿ���ڷ���ָ�������ļ�¼.
	pModelMgr=m_pModelSeek->GetModelMgrObj();	
	int nRet=pModelMgr->dbm_select_records(&suParam,RcdSet,false,cDBError);
	if(nRet!=0)
	{
		_clear_db_opera_param(suParam); // �����ѯ��Ϣ�б�
		sprintf(cLogMsg,"__DBQueryRealIed():ȫվ��ѯiedΪ%d������Ϣʱ����[%s]",nIed_Id,cDBError);
		RcdErrLogWithParentClass(cLogMsg,"TNXEcProAsdu10FJ");
		return EC_PRO_CVT_FAIL;
	}

	_clear_db_opera_param(suParam); // �����ѯ��Ϣ�б�

	//�����
	UINT nRecordNum=0;
	RcdSet.get_record_num(nRecordNum);
	if(0==nRecordNum)//û���ҵ���Ӧ�ļ�
	{
		RcdSet.clear_result();
		memset(cLogMsg,0,500);
		sprintf(cLogMsg,"__DBQueryRealIed():ȫվ��ied��,iedΪ%d�����ò�����.",nIed_Id);
		RcdErrLogWithParentClass(cLogMsg,"TNXEcProAsdu10FJ");
		return EC_PRO_CVT_FAIL;
	}
	string strValue;
	RcdSet.get_field_value_row(1,1,strValue);
	nGroup = atoi(strValue.c_str());

	RcdSet.get_field_value_row(2,1,strValue);
	nEntry = atoi(strValue.c_str());

	RcdSet.clear_result();
	memset(cLogMsg,0,500);
	sprintf(cLogMsg,"__DBQueryRealIed():��ȡIED��%d,��ţ�%d.��Ŀ�ţ�%d.",nIed_Id,nGroup,nEntry);
	RcdTrcLogWithParentClass(cLogMsg,"TNXEcProAsdu10FJ");
	return EC_PRO_CVT_SUCCESS;
}

/**
* @brief         ת��NXͨ����Ϣ�Ӽ����ݣ�ֵΪ���ͣ�Ϊ�����ͨ�÷������ݼ�ӳ���
* @param[out]     OUT GROUP2GENLIST_MAP & GToGenListMap:ͨ�÷������ݼ�ӳ���
* @return        int 0-�ɹ� ����ʧ��
*/
int TNXEcProAsdu10FJ::__CvtNxSubFieldToGenList_Int(OUT GROUP2GENLIST_MAP & GToGenListMap)
{
	char cError[255]="";
	ASDU_GEN_INFO GenData;     // ͨ�÷������ݽṹ
	ASDU_GEN_INFO_LIST * pGenList = NULL;
	u_int8 nGroup = 0,nEntry = 0,nDataType=0,nKod=0;
	bool  bGetCfg = false,bDpi = false;
	int nValue = 0;

	// �жϻ�Ӧ���ܽ��
	if(m_pCommonMsg->n_result != 0 )   // ʧ��
		return EC_PRO_CVT_FAIL;

	// ת��NX�¼��Ӽ��ṹΪͨ�÷�������
	NX_COMMON_MSG_SUBFILED_LIST::iterator ite = m_pCommonMsg->list_subfields.begin();
	while ( ite != m_pCommonMsg->list_subfields.end() )
	{
		GenData.vGid.clear();
		// ������\��Ŀ�ż�����������Ϣ
		if( ( m_pCommonMsg->n_msg_type == NX_IED_CALL_SGZONE_REP) || 
			( m_pCommonMsg->n_msg_type == NX_IED_CTRL_SGZONE_CHECK_REP) ||
			( m_pCommonMsg->n_msg_type == NX_IED_CTRL_SGZONE_EXC_REP) 
			)
		{
			bGetCfg = ___GetZoneDataPoint103Cfg(ite->n_field_id,nGroup,nEntry,nDataType);
			nValue = ite->n_value;
		}
		else if( ( m_pCommonMsg->n_msg_type == NX_IED_CALL_SOFTSTRAP_REP) ||
			( m_pCommonMsg->n_msg_type == NX_IED_CTRL_SOFTSTRAP_CHECK_REP) || 
			( m_pCommonMsg->n_msg_type == NX_IED_CTRL_SOFTSTRAP_EXC_REP)
			)
		{
			bDpi = true;
			bGetCfg = ___GetSoftDataPoint103Cfg(ite->n_field_id,nGroup,nEntry,nDataType);
			if( ite->n_value == 1 )
				nValue = 2;
			else
				nValue = 1;
		}
		else if( ( m_pCommonMsg->n_msg_type == NX_IED_CALL_HARDSTRAP_REP) ||
			( m_pCommonMsg->n_msg_type == NX_IED_CTRL_HARDSTRAP_CHECK_REP) || 
			( m_pCommonMsg->n_msg_type == NX_IED_CTRL_HARDSTRAP_EXC_REP)
			)
		{
			bDpi = true;
			bGetCfg = ___GetHardDataPoint103Cfg(ite->n_field_id,nGroup,nEntry,nDataType);
			if( ite->n_value == 1 )
				nValue = 2;
			else
				nValue = 1;
		}
		else
		{
			sprintf(cError,"__CvtNxSubFieldToGenList_Int():��֧�ֻ�ȡn_msg_type=%d��Ϣ��103������",
				m_pCommonMsg->n_msg_type);
			RcdErrLogWithParentClass(cError,"TNXEcProAsdu10FJ");
		}

		if( !bGetCfg)
		{
			++ite;
			continue;
		}

		nKod = ___GetGenKod(m_pCommonMsg->n_data_src);

		__SetGenericIntData(nGroup,nEntry,nKod,nValue,GenData,bDpi);

		// ��ͨ�÷������ݼ����б�
		if( GToGenListMap.count(nGroup) > 0 )    // �����Ѵ������ȡ��Ӧ���б�ָ��
		{
			pGenList = GToGenListMap[nGroup];
		}
		else      // ���������½�
		{
			pGenList = new ASDU_GEN_INFO_LIST;
			GToGenListMap[nGroup] = pGenList;
		}

		if( pGenList != NULL )
			pGenList->push_back(GenData);

		++ite;
		pGenList = NULL;
		nGroup = nEntry = nKod = nDataType = 0;
		bDpi  = false;
		bGetCfg = false;
		nValue = 0;
	}

	GenData.vGid.clear();
	return EC_PRO_CVT_SUCCESS;
}

/**
* @brief         ����ASDU10��Ϣ�ṹ��ʽ��ASDU10�����壨֧��ָ��RIIֵ��
* @param[in]     ASDU10_INFO &Asdu10Info:asdu10��Ϣ�ṹ
* @param[out]    PRO_FRAME_BODY_LIST  & lBody :ASDU��Լ��Ϣ���б�
* @param[in]     u_int8 nRii:������Ϣ��ʶ��
* @param[in]     int nReserve:����
* @return        int 0-�ɹ� ����ʧ��
*/
int TNXEcProAsdu10FJ::FormatAsdu10BodyAutoUp(IN ASDU10_INFO &Asdu10Info,OUT PRO_FRAME_BODY_LIST &lBody,IN u_int8 nRii,IN int nReserve)
{
	char cError[255] = "";
	int nSrcSize = lBody.size();
	int nEntryNum = 0;  // ����Ŀ��
	GROUP2GENLIST_MAP::iterator iteMap;
	ASDU_GEN_INFO_LIST * pGenList= NULL;

	// ��ʽ���������Զ����ͣ�ʹ��ָ����RIIֵ��
	iteMap = Asdu10Info.GroupToGenListMap.begin();
	while( iteMap != Asdu10Info.GroupToGenListMap.end() )
	{
		_FormatOneGroupAsdu10BodyAutoUp(Asdu10Info,iteMap->first,nRii,lBody);
		pGenList = iteMap->second;
		if( pGenList != NULL )
			nEntryNum += pGenList->size();
		++iteMap;
	}
	sprintf(cError,"FormatAsdu10BodyAutoUp():����%d���Զ�����asdu10,RII=0x%02X,addr=%d,nRii=%d,cpu=%d ����Ŀ��=%d ",
		lBody.size()-nSrcSize,nRii,Asdu10Info.Addr.nAddr,nRii,Asdu10Info.Addr.nCpu,nEntryNum);

	RcdTrcLogWithParentClass(cError,"TNXEcProAsdu10FJ");
	return 0;
}

