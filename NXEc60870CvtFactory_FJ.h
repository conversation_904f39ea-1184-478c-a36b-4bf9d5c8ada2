/**********************************************************************
* NXEc60870CvtFactory_FJ.h         author:ml      date:08/11/2025            
*---------------------------------------------------------------------
*  note: 福建103规约转换工厂头文件                                                              
*  
**********************************************************************/

#ifndef _H_NXEC60870CVTFACTORY_FJ_H_ 
#define _H_NXEC60870CVTFACTORY_FJ_H_

#include "NXEc60870CvtObj_FJ.h"
#include "NXEc60870CvtFactory.h"
#include "NXEcSrvProtocol.h"

/**
* @defgroup    CNXEc60870CvtFactory: IEC60870转换工厂基类
* @{
*/
 
/**
 * @brief      创建福建103、104转换对象的工厂继承类
 * <AUTHOR>
 * @date       03/12/2013
 *
 * example
 * @code*  
 *   
 *
 * @endcode
 */

class CNXEc60870CvtFactoryFJ:public CNXEc60870CvtFactory
{
	///////////////////////////////////////////////////////////////构造、析构
public:
	
	/**
	* @brief         析构函数
	* @param[in]     无 
	* @param[out]    无
	* @return        无
	*/
	virtual ~CNXEc60870CvtFactoryFJ();

    /**
	* @brief         构造函数 
	* @param[out]    无
	* @return        无
	*/
	CNXEc60870CvtFactoryFJ(const SRV_PRO_START_PARAM * pParam); 

	///////////////////////////////////////////////////////////////公用方法
public:

	/**
	* @brief         创建规约转换对象
	* @param[in]     INXEcSSModelSeek * pSeekIns:模型查询实例
	* @param[out]    CLogRecord * pLogRecord:日志对象指针
	* @return        INXEcProConvertObj* :转换对象指针
	*/
	virtual INXEcProConvertObj * CreateProConvertObj(INXEcSSModelSeek * pSeekIns,CLogRecord * pLogRecord);

	/**
	* @brief         销毁转换对象
	* @param[in]     INXEcProConvertObj * pConvertIns：转换对象指针
	* @param[out]    无
	* @return        bool :true-成功 false-失败
	*/
	virtual bool DestroyProConvertObj( INXEcProConvertObj * pConvertIns);


	const SRV_PRO_START_PARAM * m_pParam;
	

	///////////////////////////////////////////////////////////////////保护成员
protected:

	/** @brief         规约转化库加载对象*/

};


/** @} */ //OVER


#endif // _H_NXEC60870CVTFACTORY_FJ_H_