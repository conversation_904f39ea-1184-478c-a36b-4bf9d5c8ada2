/**********************************************************************
* NXEc60870CvtFactory_FJ.cpp         author:ml      date:08/11/2025            
*---------------------------------------------------------------------
*  note:福建103规约转换工厂继承类实现文件                                                               
*  
**********************************************************************/

#include "NXEc60870CvtFactory_FJ.h"

/**
* @brief         构造函数 
* @param[in]     无
* @param[out]    无
* @return        无
*/
CNXEc60870CvtFactoryFJ::CNXEc60870CvtFactoryFJ(const SRV_PRO_START_PARAM * pParam)
{
	if (NULL != pParam)
	{
		m_pParam = pParam;
	}
	_LoadProCvtLib();
}

/**
* @brief         析构函数
* @param[in]     无 
* @param[out]    无
* @return        无
*/
CNXEc60870CvtFactoryFJ::~CNXEc60870CvtFactoryFJ()
{
	_FreeProCvtLib();
}

/**
* @brief         创建规约转换对象
* @param[in]     INXEcSSModelSeek * pSeekIns:模型查询实例
* @param[out]    CLogRecord * pLogRecord:日志对象指针
* @return        INXEcProConvertObj* :转换对象指针
*/
INXEcProConvertObj * CNXEc60870CvtFactoryFJ::CreateProConvertObj(INXEcSSModelSeek * pSeekIns,CLogRecord * pLogRecord)
{
	CNXEc60870CvtObj * pIns = new CNXEc60870CvtObjFJ(pSeekIns,pLogRecord,m_pParam);
	if( pIns != NULL )
	{
		return pIns;
	}

	return NULL;
}

/**
* @brief         销毁转换对象
* @param[in]     INXEcProConvertObj * pConvertIns：转换对象指针
* @param[out]    无
* @return        bool :true-成功 false-失败
*/
bool CNXEc60870CvtFactoryFJ::DestroyProConvertObj( INXEcProConvertObj * pConvertIns)
{
	if( pConvertIns != NULL )
	{
		delete pConvertIns;
	}

	return true;
}


