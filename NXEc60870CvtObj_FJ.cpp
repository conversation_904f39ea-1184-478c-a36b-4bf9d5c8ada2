/**********************************************************************
* NXEc60870CvtObj_FJ.cpp         author:ml      date:08/11/2025            
*---------------------------------------------------------------------
*  note:福建103规约转换对象继承类实现文件定义                                                               
*  
**********************************************************************/

#include "NXEc60870CvtObj_FJ.h"

/**
* @brief         析构函数
* @param[in]     无 
* @param[out]    无
* @return        无
*/
CNXEc60870CvtObjFJ::~CNXEc60870CvtObjFJ()
{

}

/**
* @brief         构造函数 
* @param[in]     INXEcSSModelSeek * pSeekIns:模型查询实例
* @param[out]    CLogRecord * pLogRecord:日志对象指针
* @return        无
*/
CNXEc60870CvtObjFJ::CNXEc60870CvtObjFJ(INXEcSSModelSeek * pSeekIns,CLogRecord * pLogRecord,const SRV_PRO_START_PARAM * pParam)
	:CNXEc60870CvtObj(pSeekIns,pLogRecord)
{
	m_pParam = pParam;
	m_pModelSeek = pSeekIns;
	m_nAsduMaxLen = 2048-7;
	m_pNotOrderDevList = NULL;
	m_bRead = false;
	m_bRecord = false;
	m_strClientName = "";
	m_strChannelIp  = "";
	m_strComputer = "计算机";
}

/**
* @brief         转换规约信息到NX事件消息结构
* @param[in]     PRO_FRAME_BODY * pBody :规约信息体指针
* @param[out]    NX_EVENT_MSG_LIST & lMsg :转换生成的事件消息列表
* @return        int 0-成功 其它失败
*/
int CNXEc60870CvtObjFJ::ConvertProToEventMsg( PRO_FRAME_BODY * pBody,NX_EVENT_MSG_LIST & lMsg)
{
	if( pBody == NULL )
		return EC_PRO_CVT_FAIL;

	char cError[255] = "";
	TNXEcProAsdu *pAsdu = NULL;
	int nRet = EC_PRO_CVT_FAIL;

	switch( pBody->nType )
	{
	case 1:
		pAsdu = new TNXEcProAsdu1(m_pModelSeek,m_pLogRecord);
		break;
	case 2:
		pAsdu = new TNXEcProAsdu2(m_pModelSeek,m_pLogRecord);
		break;
	case 4:
		pAsdu = new TNXEcProAsdu4(m_pModelSeek,m_pLogRecord);
		break;
	case 10:
		pAsdu = new TNXEcProAsdu10(m_pModelSeek,m_pLogRecord);
		break;
	case 12:
		pAsdu = new TNXEcProAsdu12FJ(m_pModelSeek,m_pLogRecord);
		break;
	default:
		sprintf(cError,"ConvertProToEventMsg()中暂不支持type=%d的asdu处理",pBody->nType);
		RcdErrLogWithParentClass(cError,"CNXEc60870CvtObj");
		return EC_PRO_CVT_NOSUPPORT;
	}

	if( pAsdu != NULL )
	{
		// 设置默认属性
		_SetProProperty(pAsdu);
		nRet = pAsdu->ConvertProToEventMsg(pBody,lMsg);
		delete pAsdu;
		pAsdu = NULL;
	}

	return nRet;
}

/**
* @brief         转换规约信息到NX通用消息结构
* @param[in]     PRO_FRAME_BODY_LIST* pBodyList :规约信息体列表指针
* @param[out]    NX_COMMON_MSG_LIST & lMsg: 保存生成的通用消息列表
* @param[out]    PRO_FRAME_BODY_LIST & lResult：保存生成的规约失败回应(服务端规约有效）
* @return        >=0:成功 <0:失败
*/
int CNXEc60870CvtObjFJ::ConvertProToCommonMsg(PRO_FRAME_BODY_LIST* pBodyList,NX_COMMON_MSG_LIST & lMsg,PRO_FRAME_BODY_LIST & lResult)
{
	if( pBodyList == NULL )
		return EC_PRO_CVT_FAIL;

	char cError[255] = "";
	TNXEcProAsdu *pAsdu = NULL;
	int nRet = EC_PRO_CVT_FAIL;
	
	// 取第一条信息判断类型
	if( pBodyList->size() <= 0 )
		return EC_PRO_CVT_FAIL;
	PRO_FRAME_BODY FirstBody = pBodyList->front();


	//从ecpro.ini文件中,读取安全测试相关的配置.	
	if (!m_bRead)
	{
		__ReadIni();
	}

	//记录操作内容
	string strDecs;
	switch( FirstBody.nType )
	{
	case 6: // 对时
		strDecs = "对时操作";
		pAsdu = new TNXEcProAsdu6(m_pModelSeek,m_pLogRecord);
		break;
	case 7: // 总召
		strDecs = "总召操作";
		pAsdu = new TNXEcProAsdu7FJ(m_pModelSeek,m_pLogRecord);
		break;
	case 10:// 通用分类控制类
		pAsdu = new TNXEcProAsdu10FJ(m_pModelSeek,m_pLogRecord);
		break;
	case 13:  //从装置召唤录波文件
		strDecs = "从装置录波文件召唤操作";
		pAsdu = new TNXEcProAsdu13FJ(m_pModelSeek,m_pLogRecord);
		break;
	case 15: // 装置召录波列表
		strDecs = "从装置录波列表召唤操作";
		pAsdu = new TNXEcProAsdu15FJ(m_pModelSeek,m_pLogRecord);
		break;
	case 20: // 复归命令
		strDecs = "远方复归二次设备指示灯操作";
		pAsdu = new TNXEcProAsdu20FJ(m_pModelSeek,m_pLogRecord);
		break;
	case 21: // 装置召值
		strDecs = "从装置召值操作";
		pAsdu = new TNXEcProAsdu21FJ(m_pModelSeek,m_pLogRecord);
		break;
	default:
		sprintf(cError,"ConvertProToCommonMsg()中暂不支持type=%d的asdu处理",FirstBody.nType);
		RcdErrLogWithParentClass(cError,"CNXEc60870CvtObj");
		return EC_PRO_CVT_NOSUPPORT;
	}


	//需要记录审计日志时，将描述入库
	if (m_bRecord)
	{
		__StatusToDb(strDecs);
	}

	if( pAsdu != NULL )
	{
		// 设置默认属性
		_SetProProperty(pAsdu);
		nRet = pAsdu->ConvertProToCommonMsg(pBodyList,lMsg,lResult);
		delete pAsdu;
		pAsdu = NULL;
	}

	return nRet;
}

/**
* @brief         直接从生成结果回应，如初始化配置信息;
* @param[in]     PRO_FRAME_BODY * pBody :规约信息体指针
* @param[out]    PRO_FRAME_BODY_LIST & lResult：本地生成的结果帧体列表
* @return        int 0-成功 其它失败
*/
int CNXEc60870CvtObjFJ::DirectResFromLocal(PRO_FRAME_BODY * pBody,PRO_FRAME_BODY_LIST & lResult)
{
	if( pBody == NULL )
		return EC_PRO_CVT_FAIL;

	char cError[255] = "";
	TNXEcProAsdu *pAsdu = NULL;
	int nRet = EC_PRO_CVT_FAIL;

	//从ecpro.ini文件中,读取安全测试相关的配置.	
	if (!m_bRead)
	{
		__ReadIni();
	}

	//记录操作内容
	string strDecs;
	switch( pBody->nType )
	{
	case 7: // 子站总召
		strDecs = "子站总召操作";
		pAsdu = new TNXEcProAsdu7FJ(m_pModelSeek,m_pLogRecord);
		break;
	case 13: // 硬盘获取录波文件
		strDecs = "从硬盘获取录波文件操作";
		pAsdu = new TNXEcProAsdu13FJ(m_pModelSeek,m_pLogRecord);
		break;
	case 15: // 数据库召录波列表
		strDecs = "从数据库召录波列表操作";
		pAsdu = new TNXEcProAsdu15FJ(m_pModelSeek,m_pLogRecord);
		break;
	case 17:// 历史事件
		strDecs = "历史事件召唤操作";
		pAsdu = new TNXEcProAsdu17FJ(m_pModelSeek,m_pLogRecord);
		break;
	case 21: // 通用分类初始化配置及从数据库召值
		strDecs = "初始化配置/从数据库召值操作";
		pAsdu = new TNXEcProAsdu21FJ(m_pModelSeek,m_pLogRecord);
		break;
	case 101: //通用文件列表命令
		strDecs = "通用文件列表召唤操作";
		pAsdu = new TNXEcProAsdu101FJ(m_pModelSeek,m_pLogRecord);
		break;
	case 103: //硬盘获取通用文件
		strDecs = "从硬盘获取通用文件操作";
		pAsdu = new TNXEcProAsdu103FJ(m_pModelSeek,m_pLogRecord);
		break;
	default:
		sprintf(cError,"DirectResFromLocal()中暂不支持type=%d的asdu处理",pBody->nType);
		RcdErrLogWithParentClass(cError,"CNXEc60870CvtObj");
		return EC_PRO_CVT_NOSUPPORT;
	}

	//需要记录审计日志时，将描述入库
	if (m_bRecord)
	{
		__StatusToDb(strDecs);
	}

	if( pAsdu != NULL )
	{
		// 设置默认属性
		_SetProProperty(pAsdu);
		nRet = pAsdu->DirectResFromLocal(pBody,lResult);
		delete pAsdu;
		pAsdu = NULL;
	}

	return nRet;
}

/**
* @brief         根据NX事件信息生成规约事件列表
* @param[in]     NX_EVENT_MESSAGE * pMsg :事件信结构指针
* @param[out]    PRO_FRAME_BODY_LIST  & lBody :规约信息体
* @return        int 0-成功 其它失败
*/
int CNXEc60870CvtObjFJ::ConvertEventMsgToPro(NX_EVENT_MESSAGE* pMsg,PRO_FRAME_BODY_LIST & lBody)
{
	if( pMsg == NULL )
		return EC_PRO_CVT_FAIL;

	char cError[255] = "";
	TNXEcProAsdu * pAsdu = NULL;
	int nRet = EC_PRO_CVT_FAIL;

	switch(pMsg->n_msg_type)
	{
	case NX_IED_EVENT_EVENT_REPORT:
		pAsdu = new TNXEcProAsdu2(m_pModelSeek,m_pLogRecord);
		break;
	case NX_IED_EVENT_ALARM_REPORT:
	case NX_IED_EVENT_HARDTRAP_REPORT:
	case NX_IED_EVENT_COMMU_REPORT:
	case NX_SYS_EVENT_IED_RUNSTATUS_REPORT:
	case NX_SYS_EVENT_IED_SG_CHG_REPORT:
	case NX_SYS_EVENT_CONFIG_ALARM:
		pAsdu = new TNXEcProAsdu1FJ(m_pModelSeek,m_pLogRecord);
		break;
	case NX_IED_EVENT_SOFTTRAP_REPORT:
	case NX_SYS_EVENT_IED_SGZONE_CHG_REPORT:
	case NX_IED_CALL_ROBOTCHECK_REPORT:
		pAsdu = new TNXEcProAsdu10FJ(m_pModelSeek,m_pLogRecord);
		//pAsdu = new TNXEcProAsdu10(m_pModelSeek,m_pLogRecord);
		break;
	case NX_IED_EVENT_OSCFILE_REPORT:
		pAsdu = new TNXEcProAsdu12FJ(m_pModelSeek,m_pLogRecord);
		break;
	case NX_IED_EVENT_FILE_REPORT:
		pAsdu = new TNXEcProAsdu109FJ(m_pModelSeek,m_pLogRecord);
		break;
    //case NX_SYS_EVENT_ECU_COMMU_REPORT:
	default:
		sprintf(cError,"ConvertEventMsgToPro()中暂不支持n_msg_type=%d的消息处理",pMsg->n_msg_type);
		RcdErrLogWithParentClass(cError,"CNXEc60870CvtObj");
		return EC_PRO_CVT_NOSUPPORT;
	}

	if( pAsdu != NULL )
	{
		// 设置默认属性
		_SetProProperty(pAsdu);
		nRet = pAsdu->ConvertEventMsgToPro(pMsg,lBody);
		delete pAsdu;
		pAsdu = NULL;
	}

	return nRet;
}

/**
* @brief         根据NX通用信息及规约命令生成规约结果列表或根据通用消息生成规约命令
* @param[in]     NX_COMMON_MESSAGE * pMsg :通用信息结构指针
* @param[in][out]PRO_FRAME_BODY_LIST & lCmd:规约命令(服务端规约时为输入,客户端规约时为输出)
* @param[out]    PRO_FRAME_BODY_LIST & lResult :规约信息体列表(服务端规约有效)
* @return        int 0-成功 其它失败
*/
int CNXEc60870CvtObjFJ::ConvertCommonMsgToPro(NX_COMMON_MESSAGE * pMsg,PRO_FRAME_BODY_LIST & lCmd,PRO_FRAME_BODY_LIST & lResult)
{
	if( ( pMsg == NULL ) || (lCmd.size()<= 0) )
		return EC_PRO_CVT_FAIL;

	char cError[255] = "";
	TNXEcProAsdu * pAsdu = NULL;
	int nRet = EC_PRO_CVT_FAIL;

	switch(pMsg->n_msg_type)
	{
	
	case NX_IED_CALL_SG_REP:
	case NX_IED_CALL_SGZONE_REP:
	case NX_IED_CALL_SOFTSTRAP_REP:
	case NX_IED_CALL_ANALOG_REP:
	case NX_IED_CTRL_SG_CHECK_REP:
	case NX_IED_CTRL_SG_EXC_REP:
	case NX_IED_CTRL_SGZONE_CHECK_REP:
	case NX_IED_CTRL_SGZONE_EXC_REP:
	case NX_IED_CTRL_SOFTSTRAP_CHECK_REP:
	case NX_IED_CTRL_SOFTSTRAP_EXC_REP:
	case NX_IED_CTRL_HARDSTRAP_CHECK_REP:
	case NX_IED_CTRL_HARDSTRAP_EXC_REP:
	case NX_IED_CALL_ROBOTCHECK_REP:
	case NX_IED_CALL_61850SRV_READ_REP:
		pAsdu = new TNXEcProAsdu10FJ(m_pModelSeek,m_pLogRecord);
		break;
	case NX_IED_CALL_HARDSTRAP_REP:
		pAsdu = new TNXEcProAsdu42FJ(m_pModelSeek,m_pLogRecord);
		break;
	case NX_IED_CALL_OSCLIST_REP:
		pAsdu = new TNXEcProAsdu16FJ(m_pModelSeek,m_pLogRecord);
		break;
		//增加从装置召唤录波文件回复命令 by yys 20171214
	case NX_IED_CALL_OSCFILE_REP:
		pAsdu = new TNXEcProAsdu13FJ(m_pModelSeek,m_pLogRecord);
		break;
	case NX_IED_CTRL_IEDTRIP_REST_REP:
		pAsdu = new TNXEcProAsdu20FJ(m_pModelSeek,m_pLogRecord);
		break;
// 	case NX_IED_CALL_IEDFILELIST_REP:
// 		 pAsdu = new TNXEcProAsdu102(m_pModelSeek,m_pLogRecord);
// 		 break;
// 	case NX_IED_CALL_HISEVENT_REP:
// 		pAsdu = new TNXEcProAsdu18(m_pModelSeek,m_pLogRecord);
// 		break;
	default:
		sprintf(cError,"ConvertCommonMsgToPro()中暂不支持n_msg_type=%d的消息处理",pMsg->n_msg_type);
		RcdErrLogWithParentClass(cError,"CNXEc60870CvtObj");
		return EC_PRO_CVT_NOSUPPORT;
	}

	if( pAsdu != NULL )
	{
		// 设置默认属性
		_SetProProperty(pAsdu);
		nRet = pAsdu->ConvertCommonMsgToPro(pMsg,lCmd,lResult);
		delete pAsdu;
		pAsdu = NULL;
	}

	return nRet;
}

/**
* @brief         设置规约属性
* @param[in]     TNXEcProAsdu * pAsdu:asdu对象
* @param[out]    无
* @return        void
*/
void CNXEc60870CvtObjFJ::_SetProProperty(TNXEcProAsdu * pAsdu)
{
	if( pAsdu == NULL )
		return ;
	pAsdu->SetProTimeFormat(true);       // cp56格式时间
	pAsdu->SetIsHaveRcvTime(true);      // 不带本地接收时间
	pAsdu->SetCotSupportTestInfo(true); // 传输原因支持标识检修信息
	pAsdu->SetFaultParamFormat(true);   // 故障量以专用报文上送
	pAsdu->SetAsduMaxLen(m_nAsduMaxLen); // 设置asdu最大长度
	pAsdu->SetNotOrderDevList(m_pNotOrderDevList); //设置不订阅设备列表指针

}



/**
* @brief         根据规约信息体内容获得ASDU13转换类型
* @param[in]     PRO_FRAME_BODY * pBody :规约信息体指针
* @param[out]    无
* @return        EC_PRO_CVT_TYPE:转换类型
*/
EC_PRO_CVT_TYPE CNXEc60870CvtObjFJ::_GetAsdu13CvtType( PRO_FRAME_BODY *pBody)
{
	char cError[255] = "";
	if( pBody == NULL )
		return CVT_FROM_LOCAL;

	if( pBody->nInf == 2)         // 2-从装置召唤；0,1-子站召唤录波文件/录波简化文件
	{
		return CVT_TO_CALL;
	}

	//召唤录波文件,先判断本地是否存在该文件,如果存在,则从本地获取,如果不存在则下发召唤命令.
	if ( pBody->vVarData.size() < 73)
	{
		RcdErrLogWithParentClass("收到召唤录波文件命令，但命令报文长度不足，无法解析出召唤的文件名。","CNXEc60870CvtObjFJ");
		return CVT_UNKNOWN;
	}
	char cFileName[65] = "";
	memcpy(cFileName,&pBody->vVarData[1],64);
	if (_WaveFilesExistInLocal(pBody->nAddr,cFileName))
	{
		// 从本地召唤录波文件
		return CVT_FROM_LOCAL;
	}
	
	sprintf(cError,"_GetAsdu13CvtType():收到召唤设备(103addr=%d)的录波文件%s,但在子站本地没有发现对应的cfg和dat文件,改从装置直接召唤.",pBody->nAddr,cFileName);
	RcdTrcLogWithParentClass(cError,"CNXEc60870CvtObjFJ");

	return CVT_TO_CALL; //没有则从装置召唤.

}

/**
* @brief		判断设备的指定文件名(不含后缀)的cfg和dat文件是否存在.			
* @param[in]    nAddr103:设备103地址
* @param[in]    cFileName:文件名(不含后最)
* @return		0-执行成功；其他-执行失败
**/
bool CNXEc60870CvtObjFJ::_WaveFilesExistInLocal(int nAddr103, char * cFileName)
{
	char cFilePath[500]= "";
	vector<string> vExt;
	string strExt;
	bool bCfg = false;
	bool bDat = false;

	__MakeIedComtradePath(nAddr103,cFilePath);	
	if (!__GetFileExts(cFilePath,cFileName,vExt)) 
		return false;
	for (int i = 0 ; i < vExt.size() ; i++ )
	{
		strExt = vExt[i];
		transform(strExt.begin(),strExt.end(),strExt.begin(),::toupper);
		if ( strExt == "CFG")
		{
			bCfg = true;
		}
		if ( strExt == "DAT")
		{
			bDat = true;
		}
	}
	return (bCfg && bDat);
}

/***********************************************************
* @brief		根据IED做成该设备保存录波的完整路径信息.			
* @param[in]    Addr103:设备103地址
* @param[out]   cIedComtradePath:路径信息
* @return		0-执行成功；其他-执行失败
* @note 
***********************************************************/
void CNXEc60870CvtObjFJ::__MakeIedComtradePath(int Addr103,char* cIedComtradePath)
{
	char   cError[255]="";
	char   cTempPath[255]="";

	//获取数据库中配置的录波总路径
	BASIC_CFG_TB BasicCfg;
	m_pModelSeek->GetBasicCfg(BasicCfg);
	sy_format_file_path(BasicCfg.str_file_path.c_str(),cTempPath);

	//获取子站名称:
	const SUBSTATION_TB * pStation =  m_pModelSeek->GetSubStationBasicCfg();
	if( pStation == NULL )
		return ;
	string sSubName = pStation->str_aliasname;

	// 根据103地址获得设备名称、设备id及设备所属的变电站ID
	const IED_TB * pIed = m_pModelSeek->GetIedBasicCfgByAddr103(Addr103);
	if( pIed == NULL )
	{
		sprintf(cError,"__MakeIedComtradePath():获得103Addr=%d的设备配置失败,无法生成路径",Addr103);
		RcdErrLogWithParentClass(cError,"CNXEc60870CvtObjFJ");
		return ;
	}
	int nIedId = pIed->n_obj_id;
	string sIedName = pIed->str_aliasname;

	sprintf(cIedComtradePath,"%s%s%sIED_%d_%s%s",cTempPath,sSubName.c_str(),FILE_PATH_OPT_STR,nIedId,sIedName.c_str(),FILE_PATH_OPT_STR);
	RcdTrcLogWithParentClass(cIedComtradePath,"CNXEc60870CvtObjFJ");
}

/**********************************************************
* @brief		获取指定目录下,具有相同文件名的所有后缀..			
* @param[in]    char* cWaveFileName:录波文件名
* @param[in]    vStr& vWaveFileExt:当查到本地磁盘有该文件存在,则将该文件的所有后缀名获取到队列中.
* @return		0-执行成功；其他-执行失败
* @note 
**********************************************************/
bool CNXEc60870CvtObjFJ::__GetFileExts(char* cWaveFilePath,char* cWaveFileName,vStr& vWaveFileExt)
{

	char cFilePath[255]="";//解析出的文件路径
	char cFileName[255]="";//解析出的文件名称
	char cFileExt[20]="";  //解析出的后缀名称
	bool bRet=false;

	vector <_FILE_PROPERTY_INF> vFilePropertyList;
	vector <_FILE_PROPERTY_INF>::iterator ite;
	//int nRet=sy_get_files_property_indir(cWaveFilePath,&vFilePropertyList);//获取该目录下所有文件的属性
	// 获取同名文件的所有后缀名相关文件属性
	int nRet = _get_all_files_property_byName(cWaveFilePath,cWaveFileName,&vFilePropertyList);
	if (nRet!=0) 
		return bRet;

	if (vFilePropertyList.size() == 0) 
		return bRet;

	ite=vFilePropertyList.begin();
	while(ite != vFilePropertyList.end())
	{
		memset(cFilePath,0,strlen(cFilePath));
		memset(cFileName,0,strlen(cFileName));
		memset(cFileExt,0,strlen(cFileExt));
		sy_get_file_name(ite->chName,cFilePath,cFileName,cFileExt);
		// 		nRet=strcmp(cFileName,cWaveFileName); 
		// 		if(nRet != 0) //文件名不相同
		// 		{
		// 			++ite;
		// 			continue;
		// 		}
		vWaveFileExt.push_back(cFileExt); //将对应的后缀名压到队列中.
		bRet=true;
		++ite;
	}
	vFilePropertyList.clear();
	return bRet;
}
void CNXEc60870CvtObjFJ::__StatusToDb(string & strDecs)
{
	if ( NULL == m_pModelSeek)
	{
		RcdErrLogWithParentClass("获取m_pModelSeek指针失败.","CNXEc60870CvtObjFJ");
		return;
	}

	INXEcModelMgr * pModelMgr = m_pModelSeek->GetModelMgrObj();

	if ( NULL == pModelMgr)
	{
		RcdErrLogWithParentClass("获取pModelMgr指针失败.","CNXEc60870CvtObjFJ");
		return;
	}

	RcdTrcLogWithParentClass("将审计日志写入数据库.","CNXEc60870CvtObjFJ");

	char cDecs[255]= "";

	/*sprintf(cDecs,"%s,IP:%s：%s",m_strClientName.c_str(),m_strChannelIp.c_str(),strDecs.c_str());*/

	sprintf(cDecs,"%s,通道号:%d：%s",m_pParam->ClientCfg.strCliName.c_str(),m_pParam->ClientCfg.n_channel_id,strDecs.c_str());

	strDecs = cDecs;

	DB_OPER_PARAM DbOperParam;
	DB_FIELD_DATA suField;
	char cDbInsertErr[200] = "";
	char cErr[600]="";

	suField.str_fd_name		= "usr_obj";
	suField.str_fd_value	= "ecsrv";
	suField.e_fd_type		= FD_TYPE_CHAR;
	DbOperParam.lst_fddata.push_back(suField);

	suField.str_fd_name		= "compname";
	suField.str_fd_value	= m_strComputer;
	suField.e_fd_type		= FD_TYPE_CHAR;
	DbOperParam.lst_fddata.push_back(suField);

	suField.str_fd_name		= "oprtm";
	char cTmp[512]="";
	time_t tSys = time(NULL);
	struct tm *local = localtime(&tSys); 
	strftime(cTmp,100,"%Y-%m-%d %H:%M:%S",local);
	suField.str_fd_value = cTmp;
	suField.e_fd_type		= FD_TYPE_DATETIME;
	DbOperParam.lst_fddata.push_back(suField);

	suField.str_fd_name		= "function_obj";
	suField.str_fd_value	= "sysadmin_ecsrv";
	suField.e_fd_type		= FD_TYPE_CHAR;
	DbOperParam.lst_fddata.push_back(suField);

	suField.str_fd_name		= "log_desc";
	suField.str_fd_value	= strDecs;
	suField.e_fd_type		= FD_TYPE_CHAR;
	DbOperParam.lst_fddata.push_back(suField);

	suField.str_fd_name		= "oprresult";
	suField.str_fd_value	=  "1";
	suField.e_fd_type		= FD_TYPE_NUMERIC;
	DbOperParam.lst_fddata.push_back(suField);

	DbOperParam.lst_tablename.push_back("nx_t_usr_oprlog");

	if ( 0 != pModelMgr->dbm_insert_record(&DbOperParam,cDbInsertErr))
	{
		sprintf(cErr,"审计日志:%s 写库失败.",strDecs.c_str());
		RcdErrLogWithParentClass(cErr,"CNXEc60870CvtObjFJ");
	}

}
void CNXEc60870CvtObjFJ::__ReadIni()
{
	int nTemp;

	CIniOperate clMyIni;
	if (!clMyIni.SetIniFileName("ecpro.ini")) 
	{
		printf("[CYKStrapRead:__YKReadIni] 没有找到配置文件'ecpro.ini'!.\n");
	}
	else	
	{
		clMyIni.ParseIni();
		clMyIni.GetPrivateProfileInt("TEST","INFO_RECORD",0,nTemp);//是否记录主站信息到审计日志中.0-不启用,1-启用.
		m_bRecord = nTemp == 0 ? false:true;
		clMyIni.GetPrivateProfileStr("TEST","COMPUTER","",m_strComputer);//获取计算机名
		clMyIni.GetPrivateProfileStr("TEST","CLIENT_NAME","",m_strClientName);//获取计算机名
		clMyIni.GetPrivateProfileStr("TEST","CLIENT_IP","",m_strChannelIp);//获取计算机IP
	}

	m_bRead = true;
}
