/**********************************************************************
* NXEc60870CvtObj_FJ.h         author:ml      date:08/11/2025            
*---------------------------------------------------------------------
*  note: 福建规约转换对象继承类头文件定义                                                              
*  
**********************************************************************/

#ifndef _H_NXEC60870CVTOBJ_FJ_H_ 
#define _H_NXEC60870CVTOBJ_FJ_H_

#include "NXEc60870CvtObj.h"
#include "NXEcProAsdu1_FJ.h"
#include "NXEcProAsdu7_FJ.h"
#include "NXEcProAsdu10_FJ.h"
#include "NXEcProAsdu12_FJ.h"
#include "NXEcProAsdu13_FJ.h"
#include "NXEcProAsdu15_FJ.h"
#include "NXEcProAsdu16_FJ.h"
#include "NXEcProAsdu17_FJ.h"
#include "NXEcProAsdu21_FJ.h"
#include "NXEcProAsdu42_FJ.h"
#include "NXEcProAsdu101_FJ.h"
#include "NXEcProAsdu103_FJ.h"
#include "NXEcProAsdu109_FJ.h"
#include "NXEcProAsdu20_FJ.h"
#include "NXEcSrvProtocol.h"
/**
* @defgroup   CNXEc60870CvtObjFJ：60870规约转换对象继承类 
* @{
*/
 
/**
 * @brief      福建IEC103、104规约转换对象继承类头文件定义
 * <AUTHOR>
 * @date       03/12/2013
 *
 * example
 * @code*  
 *   
 *
 * @endcode
 */

class CNXEc60870CvtObjFJ:public CNXEc60870CvtObj
{
	///////////////////////////////////////////////////////////////构造、析构
public:
	
	/**
	* @brief         析构函数
	* @param[in]     无 
	* @param[out]    无
	* @return        无
	*/
	virtual ~CNXEc60870CvtObjFJ();

    /**
	* @brief         构造函数 
	* @param[in]     INXEcSSModelSeek * pSeekIns:模型查询实例
	* @param[out]    CLogRecord * pLogRecord:日志对象指针
	* @return        无
	*/
	CNXEc60870CvtObjFJ(INXEcSSModelSeek * pSeekIns,CLogRecord * pLogRecord,const SRV_PRO_START_PARAM * pParam);

	///////////////////////////////////////////////////////////////公用方法
public:	

	/**
	* @brief         转换规约信息到NX事件消息结构
	* @param[in]     PRO_FRAME_BODY * pBody :规约信息体指针
	* @param[out]    NX_EVENT_MSG_LIST & lMsg :转换生成的事件消息列表
	* @return        int 0-成功 其它失败
	*/
	int ConvertProToEventMsg( PRO_FRAME_BODY * pBody,NX_EVENT_MSG_LIST & lMsg) ;

	/**
	* @brief         转换规约信息到NX通用消息结构
	* @param[in]     PRO_FRAME_BODY_LIST* pBodyList :规约信息体列表指针
	* @param[out]    NX_COMMON_MSG_LIST & lMsg: 保存生成的通用消息列表
	* @param[out]    PRO_FRAME_BODY_LIST & lResult：保存生成的规约失败回应(服务端规约有效）
	* @return        >=0:成功 <0:失败
	*/
	int ConvertProToCommonMsg(PRO_FRAME_BODY_LIST* pBodyList,NX_COMMON_MSG_LIST & lMsg,PRO_FRAME_BODY_LIST & lResult);

	/**
	* @brief         直接从本地生成结果回应，如初始化配置信息;
	* @param[in]     PRO_FRAME_BODY * pBody :规约信息体指针
	* @param[out]    PRO_FRAME_BODY_LIST & lResult：本地生成的结果帧体列表
	* @return        int 0-成功 其它失败
	*/
	int DirectResFromLocal(PRO_FRAME_BODY * pBody,PRO_FRAME_BODY_LIST & lResult) ;

	/**
	* @brief         根据NX事件信息生成规约事件列表
	* @param[in]     NX_EVENT_MESSAGE * pMsg :事件信结构指针
	* @param[out]    PRO_FRAME_BODY_LIST  & lBody :规约信息体
	* @return        int 0-成功 其它失败
	*/
	int ConvertEventMsgToPro(NX_EVENT_MESSAGE* pMsg,PRO_FRAME_BODY_LIST & lBody) ;

	/**
	* @brief         根据NX通用信息及规约命令生成规约结果列表或根据通用消息生成规约命令
	* @param[in]     NX_COMMON_MESSAGE * pMsg :通用信息结构指针
	* @param[in][out]PRO_FRAME_BODY_LIST & lCmd:规约命令(服务端规约时为输入,客户端规约时为输出)
	* @param[out]    PRO_FRAME_BODY_LIST & lResult :规约信息体列表(服务端规约有效)
	* @return        int 0-成功 其它失败
	*/
	int ConvertCommonMsgToPro(NX_COMMON_MESSAGE * pMsg,PRO_FRAME_BODY_LIST & lCmd,PRO_FRAME_BODY_LIST & lResult);

	/**
	* @brief         设置规约属性
	* @param[in]     TNXEcProAsdu * pAsdu:asdu对象
	* @param[out]    无
	* @return        void
	*/
	void _SetProProperty(TNXEcProAsdu * pAsdu);

	/**
	* @brief         根据规约信息体内容获得ASDU13转换类型
	* @param[in]     PRO_FRAME_BODY * pBody :规约信息体指针
	* @param[out]    无
	* @return        EC_PRO_CVT_TYPE:转换类型
	*/
	EC_PRO_CVT_TYPE _GetAsdu13CvtType( PRO_FRAME_BODY *pBody);

	/**
	* @brief		判断设备的指定文件名(不含后缀)的cfg和dat文件是否存在.			
	* @param[in]    nAddr103:设备103地址
	* @param[in]    cFileName:文件名(不含后最)
	* @return		0-执行成功；其他-执行失败
	**/
	bool _WaveFilesExistInLocal(int nAddr103, char * cFileName);


	/**
	* @brief		根据IED做成该设备保存录波的完整路径信息.			
	* @param[in]    Addr103:设备103地址
	* @param[out]   cIedComtradePath:路径信息
	* @return		0-执行成功；其他-执行失败
	* @note 
	**/
	void __MakeIedComtradePath(int Addr103,char* cIedComtradePath);

	/**
	* @brief		获取指定目录下,具有相同文件名的所有后缀..			
	* @param[in]    char* cWaveFileName:录波文件名
	* @param[in]    vStr& vWaveFileExt:当查到本地磁盘有该文件存在,则将该文件的所有后缀名获取到队列中.
	* @return		0-执行成功；其他-执行失败
	* @note 
	**/
	bool __GetFileExts(char* cWaveFilePath,char* cWaveFileName,vStr& vWaveFileExt);
	

	/** @brief              记录审计日志标识*/
	bool                    m_bRecord;

	/** @brief              计算机名称*/
	string                  m_strComputer;

	/** @brief              主站名称*/
	string                  m_strClientName;

	/** @brief              主站IP*/
	string                  m_strChannelIp;

	const SRV_PRO_START_PARAM * m_pParam;

	////////////////////////////////////////////////////////////////////////私有方法
private:
	/**
	* @brief 从ecpro.ini文件中,读取安全测试相关的配置.					
	* @note 
	**/
	void __ReadIni();
	/**
	* @brief 将状态信息写入数据库.					
	* @note 
	**/
	void __StatusToDb(string & strDecs);

	/** @brief              是否已经读取配置文档*/
	bool					m_bRead;

	////////////////////////////////////////////////////////////////////////私有成员
private:
};

/** @} */ //OVER


#endif // _H_NXEC60870CVTOBJ__FJ_H_