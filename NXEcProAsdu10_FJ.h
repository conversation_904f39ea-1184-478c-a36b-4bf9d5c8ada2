/**********************************************************************
* NXEcProAsdu10.h         author:ml      date:08/11/2025            
*---------------------------------------------------------------------
*  note:ASDU10报文转换处理头文件                                                                 
*  
**********************************************************************/

#ifndef _H_NXECPROASDU10_HZS_H_ 
#define _H_NXECPROASDU10_HZS_H_

#include "NXEcProAsdu10.h"
#include "NXEcProXmlHdl.h"
extern CCvtDataToXml gCvtXml_10;

class TNXEcProAsdu10FJ:public TNXEcProAsdu10
{
	///////////////////////////////////////////////////////////////构造、析构
public:
	
	/**
	* @brief         析构函数
	* @param[in]     无 
	* @param[out]    无
	* @return        无
	*/
	virtual ~TNXEcProAsdu10FJ();

    /**
	* @brief         构造函数 
	* @param[in]     INXEcSSModelSeek * pSeekIns:模型查询实例
	* @param[out]    CLogRecord * pLogRecord:日志对象指针
	* @return        无
	*/
	TNXEcProAsdu10FJ(IN INXEcSSModelSeek * pSeekIns,IN CLogRecord * pLogRecord);



	///////////////////////////////////////////////////////////////公用方法
public:
		/**
	* @brief         根据NX通用信息及规约命令生成规约结果列表或根据通用消息生成规约命令
	* @param[in]     NX_COMMON_MESSAGE * pMsg :通用信息结构指针
	* @param[in][out]PRO_FRAME_BODY_LIST & lCmd:规约命令
	* @param[out]    PRO_FRAME_BODY_LIST & lResult :规约信息体列表
	* @return        int 0-成功 其它失败
	*/
	virtual int ConvertCommonMsgToPro(IN NX_COMMON_MESSAGE * pMsg,IN OUT PRO_FRAME_BODY_LIST & lCmd,OUT PRO_FRAME_BODY_LIST & lResult);
	/**
	* @brief         转换规约信息到NX通用消息结构
	* @param[in]     PRO_FRAME_BODY_LIST* pBodyList :规约信息体列表指针
	* @param[out]    NX_COMMON_MSG_LIST & lMsg: 保存生成的通用消息列表
	* @param[out]    PRO_FRAME_BODY_LIST & lBody：保存生成的规约失败回应(服务端规约有效）
	* @return        int 0-成功 其它失败
	*/
	virtual int ConvertProToCommonMsg(IN PRO_FRAME_BODY_LIST* pBodyList,OUT NX_COMMON_MSG_LIST & lMsg,OUT PRO_FRAME_BODY_LIST & lBody);

	////////////////////////////////////////////////////////////////////////保护方法
protected:
	///////////////////////////////////////////////////////////////////////
protected:
	/**
	* @brief         转换NX软压板变位事件信息到规约事件列表
	* @param[out]    PRO_FRAME_BODY_LIST  & lBody :规约信息体
	* @return        int 0-成功 其它失败
	*/
	virtual int _CvtSoftReportToPro(OUT PRO_FRAME_BODY_LIST & lBody);

	/**
	* @brief         转换NX定值区变化事件信息到规约事件列表
	* @param[out]    PRO_FRAME_BODY_LIST  & lBody :规约信息体
	* @return        int 0-成功 其它失败
	*/
	virtual int _CvtZoneChgReportToPro(OUT PRO_FRAME_BODY_LIST & lBody);
	/**
	* @brief         根据ASDU10信息转换为NX机器人巡视命令
	* @param[in]     ASDU10_INFO &Asdu10Info:asdu10信息结构
	* @param[out]    NX_COMMON_MSG_LIST& lMsg:通用消息列表
	* @return        int 0-成功 其它失败
	*/
	virtual int _CvtRobotProToNX(IN ASDU10_INFO & Asdu10Info,OUT NX_COMMON_MSG_LIST& lMsg);
	/**
	* @brief         转换为NX机器人巡视命令
	* @param[in]     ASDU10_INFO &Asdu10Info:asdu10信息结构
	* @param[out]    NX_COMMON_MSG_LIST& lMsg:通用消息列表
	* @return        int 0-成功 其它失败
	*/
	virtual int __CvtRotInfoToCommonMsg(IN ASDU10_INFO & Asdu10Info,OUT NX_COMMON_MESSAGE &CommonMsg);

	/**
	* @brief         转换NX机器人巡视报告事件信息到规约事件列表
	* @param[out]    PRO_FRAME_BODY_LIST  & lBody :规约信息体
	* @return        int 0-成功 其它失败
	*/
	virtual int _CvtRobertCheckReportToPro(OUT PRO_FRAME_BODY_LIST & lBody);

	/**
	* @brief         根据ASDU10信息转换为NX61850读值服务
	* @param[in]     ASDU10_INFO &Asdu10Info:asdu10信息结构
	* @param[out]    NX_COMMON_MSG_LIST& lMsg:通用消息列表
	* @return        int 0-成功 其它失败
	*/
	virtual int _CvtProToNX61850Read(IN ASDU10_INFO & Asdu10Info,OUT NX_COMMON_MSG_LIST& lMsg);
	/**
	* @brief         转换为NX61850读值服务命令
	* @param[in]     ASDU10_INFO &Asdu10Info:asdu10信息结构
	* @param[out]    NX_COMMON_MSG_LIST& lMsg:通用消息列表
	* @return        int 0-成功 其它失败
	*/
	virtual int __CvtReadPointInfoToCommonMsg(IN ASDU10_INFO & Asdu10Info,OUT NX_COMMON_MESSAGE &CommonMsg);
	/**
	* @brief         转换NX通用消息子集数据（值为整型）为组号与通用分类数据集映射表
	* @param[out]     OUT GROUP2GENLIST_MAP & GToGenListMap:通用分类数据集映射表
	* @return        int 0-成功 其它失败
	*/
	virtual int __CvtNxSubFieldToGenList_IntAuToUp(OUT GROUP2GENLIST_MAP & GToGenListMap,OUT int& nCpu,OUT int& nAddr);
	/**
	* @brief         转换NX通用消息子集数据（机器人专用）为组号与通用分类数据集映射表
	* @param[out]     OUT GROUP2GENLIST_MAP & GToGenListMap:通用分类数据集映射表
	* @return        int 0-成功 其它失败
	*/
	virtual int __CvtNxSubFieldToGenList_IntAuToUp_Robert(OUT GROUP2GENLIST_MAP & GToGenListMap,OUT int& nCpu,OUT int& nAddr);
	/**
	* @brief         根据ASDU10信息及召唤命令产生为规约报文体格式列表
	* @param[in]     nCvtResult:转换结果 成功或失败
	* @param[in]     ASDU10_INFO &Asdu10Info:asdu10信息结构
	* @param[out]    PRO_FRAME_BODY_LIST& lResult:报文体结果列表
	* @return        int 0-成功 其它失败
	*/
	virtual int __CvtAsdu10InfoToProBodyAuToUp(IN int nCvtResult,IN int nCpu,IN int nAddr,IN ASDU10_INFO & Asdu10Info,OUT PRO_FRAME_BODY_LIST & lResult);
	/**
	* @brief         根据ASDU10信息及召唤命令产生为规约报文体格式列表
	* @param[in]     nCvtResult:转换结果 成功或失败
	* @param[in]     ASDU10_INFO &Asdu10Info:asdu10信息结构
	* @param[out]    PRO_FRAME_BODY_LIST& lResult:报文体结果列表
	* @return        int 0-成功 其它失败
	*/
	virtual int __CvtAsdu10InfoToProBodyAuToUp_Rot(IN int nCvtResult,IN int nCpu,IN int nAddr,IN ASDU10_INFO & Asdu10Info,OUT PRO_FRAME_BODY_LIST & lResult);

		/**
	* @brief         根据ASDU10信息结构格式化ASDU10报文体
	* @param[in]     ASDU10_INFO &Asdu10Info:asdu10信息结构
	* @param[out]    PRO_FRAME_BODY_LIST  & lBody :ASDU规约信息体列表
	* @param[in]     int nReserve:备用
	* @return        int 0-成功 其它失败
	*/
	virtual int FormatAsdu10BodyAutoUp(IN ASDU10_INFO &Asdu10Info,OUT PRO_FRAME_BODY_LIST &lBody,IN int nReserve=0);
	/**
	* @brief         根据ASDU10信息结构格式化ASDU10报文体（支持指定RII值）
	* @param[in]     ASDU10_INFO &Asdu10Info:asdu10信息结构
	* @param[out]    PRO_FRAME_BODY_LIST  & lBody :ASDU规约信息体列表
	* @param[in]     u_int8 nRii:返回信息标识符
	* @param[in]     int nReserve:备用
	* @return        int 0-成功 其它失败
	*/
	virtual int FormatAsdu10BodyAutoUp(IN ASDU10_INFO &Asdu10Info,OUT PRO_FRAME_BODY_LIST &lBody,IN u_int8 nRii,IN int nReserve=0);
		/**
	* @brief         根据ASDU10信息结构格式化ASDU10报文体
	* @param[in]     ASDU10_INFO &Asdu10Info:asdu10信息结构
	* @param[out]    PRO_FRAME_BODY_LIST  & lBody :ASDU规约信息体列表
	* @param[in]     int nReserve:备用
	* @return        int 0-成功 其它失败
	*/
	virtual int FormatAsdu10BodyAutoUp_Rot(IN ASDU10_INFO &Asdu10Info,OUT PRO_FRAME_BODY_LIST &lBody,IN int nReserve=0);

	/**
	* @brief         根据ASDU10信息结构格式化指定组号的ASDU10报文体
	* @param[in]     ASDU10_INFO &Asdu10Info:asdu10信息结构
	* @param[in]     u_int8 nGroup:指定组号
	* @param[in]     u_int8 nRii:返回信息标识符
	* @param[out]    PRO_FRAME_BODY_LIST  & lBody :ASDU规约信息体列表
	* @return        int 0-成功 其它失败
	*/
	virtual int _FormatOneGroupAsdu10BodyAutoUp(IN ASDU10_INFO &Asdu10Info,IN u_int8 nGroup,IN u_int8 nRii,OUT PRO_FRAME_BODY_LIST &lBody);
	/**
	* @brief         根据ASDU10信息结构格式化指定组号的ASDU10报文体
	* @param[in]     ASDU10_INFO &Asdu10Info:asdu10信息结构
	* @param[in]     u_int8 nGroup:指定组号
	* @param[in]     u_int8 nRii:返回信息标识符
	* @param[out]    PRO_FRAME_BODY_LIST  & lBody :ASDU规约信息体列表
	* @return        int 0-成功 其它失败
	*/
	virtual int _FormatOneGroupAsdu10BodyAutoUp_Rot(IN ASDU10_INFO &Asdu10Info,IN u_int8 nGroup,IN u_int8 nRii,OUT PRO_FRAME_BODY_LIST &lBody);

	/**
	* @brief         获得定值区号信息点的103配置
	* @param[in]     nID:信息点编号
	* @param[out]    nGroup:组号
	* @param[out]    nItem: 条目号
	* @param[out]    nDataType:数据类型
	* @return        bool:true-成功 false-失败
	*/
	virtual bool ___GetZoneDataPoint103CfgAutoUp(IN int nIed,IN int nLd,IN int nID,OUT u_int8 &nGroup,OUT u_int8 &nEntry,OUT u_int8 &nDataType);

	/**
	* @brief         获得软压板信息点的103配置
	* @param[in]     nID:信息点编号
	* @param[out]    nGroup:组号
	* @param[out]    nItem: 条目号
	* @param[out]    nDataType:数据类型
	* @return        bool:true-成功 false-失败
	*/
	virtual bool ___GetSoftDataPoint103CfgAutoUp(IN int nIed,IN int nLd,IN int nID,OUT u_int8 &nGroup,OUT u_int8 &nEntry,OUT u_int8 &nDataType);
	/**
	* @brief         根据ASDU10信息和消息类型转换为指定的NX通用消息结构
	* @param[in]     int nMsgType:消息类型
	* @param[in]     ASDU10_INFO &Asdu10Info:asdu10信息结构
	* @param[out]    NX_COMMON_MESSAGE &CommonMsg:通用消息
	* @return        int 0-成功 其它失败
	*/
	//用基类中的方法
	//virtual int __CvtAsdu10InfoStructToCommonMsg(IN int nMsgType,IN ASDU10_INFO & Asdu10Info,OUT NX_COMMON_MESSAGE &CommonMsg);
	/**
	* @brief         根据ASDU10信息转换为远方控制预校通用消息
	* @param[in]     IN int nGroupType:组标题类型
	* @param[in]     ASDU10_INFO &Asdu10Info:asdu10信息结构
	* @param[out]    NX_COMMON_MSG_LIST& lMsg:通用消息列表
	* @return        int 0-成功 其它失败
	*/
	virtual int _CvtAsdu10InfoToRCtlCheck(IN int nGroupType,IN ASDU10_INFO & Asdu10Info,OUT NX_COMMON_MSG_LIST& lMsg);

	/**
	* @brief         根据ASDU10信息转换为远方控制执行通用消息
	* @param[in]     IN int nGroupType:组标题类型
	* @param[in]     ASDU10_INFO &Asdu10Info:asdu10信息结构
	* @param[out]    NX_COMMON_MSG_LIST& lMsg:通用消息列表
	* @return        int 0-成功 其它失败
	*/
	virtual int _CvtAsdu10InfoToRCtlExc(IN int nGroupType,IN ASDU10_INFO & Asdu10Info,OUT NX_COMMON_MSG_LIST& lMsg);
	/**
	* @brief         根据ASDU10信息转换为NX硬压板投退预校命令
	* @param[in]     ASDU10_INFO &Asdu10Info:asdu10信息结构
	* @param[out]    NX_COMMON_MSG_LIST& lMsg:通用消息列表
	* @return        int 0-成功 其它失败
	*/
	virtual int _CvtAsdu10ToHardCheck(IN ASDU10_INFO & Asdu10Info,OUT NX_COMMON_MSG_LIST& lMsg);
	/**
	* @brief         根据ASDU10信息转换为NX硬压板投退执行命令
	* @param[in]     ASDU10_INFO &Asdu10Info:asdu10信息结构
	* @param[out]    NX_COMMON_MSG_LIST& lMsg:通用消息列表
	* @return        int 0-成功 其它失败
	*/
	virtual int _CvtAsdu10ToHardExc(IN ASDU10_INFO & Asdu10Info,OUT NX_COMMON_MSG_LIST& lMsg);
		/**
	* @brief        获得硬压板信息点的内部ID编号
	* @param[in]    nIedId:设备ID
	* @param[in]    nCPU:设备CPU号
	* @param[in]    nGroup:组号
	* @param[in]    nItem: 条目号
	* @return       int :信息点ID ,当获取失败时返回-1
	*/
	virtual int ___GetHardPointIDBy103Info(IN int nIedId,IN int nCpu,IN u_int8 nGroup,IN u_int8 nEntry);
	/*****************************************************************
	* @brief         根据开关量从数据库中查找指定设备ID.
	* @param[in]     int nIed_Id:设备ID.
	* @param[out]    INT nStation_Id: 厂站ID.
	* @return        int 0-成功 其它失败
	****************************************************************/
	int __DBQueryIedByBack(IN int &nBack,OUT int & nIed,OUT int & nCpu);
	/*****************************************************************
	* @brief         根据定值名称从数据库中查找对应组号条目号.
	* @param[in]     string strSgName:定值名称.
	* @param[out]    INT nGroup: 组号.
	* @param[out]    INT nEntry: 条目号.
	* @return        bool true-成功 false-失败
	****************************************************************/
	bool ___GetGroupCfg(IN char *cSgName,OUT u_int8 & nGroup,OUT u_int8 & nEntry);
		/**
	* @brief         根据NX硬压板预校回应结果及召唤命令生成规约结果列表
	* @param[in]     PRO_FRAME_BODY_LIST & lCmd:规约命令
	* @param[out]    PRO_FRAME_BODY_LIST & lResult :规约信息体列表
	* @return        int 0-成功 其它失败
	*/
	virtual int _CvtHardCheckResultToPro(IN PRO_FRAME_BODY_LIST & lCmd,OUT PRO_FRAME_BODY_LIST & lResult);

	/**
	* @brief         根据NX硬压板执行回应结果及召唤命令生成规约结果列表
	* @param[in]     PRO_FRAME_BODY_LIST & lCmd:规约命令
	* @param[out]    PRO_FRAME_BODY_LIST & lResult :规约信息体列表
	* @return        int 0-成功 其它失败
	*/
	virtual int _CvtHardExcResultToPro(IN PRO_FRAME_BODY_LIST & lCmd,OUT PRO_FRAME_BODY_LIST & lResult);
	/**
	* @brief         根据NX机器人巡视执行回应结果及召唤命令生成规约结果列表
	* @param[in]     PRO_FRAME_BODY_LIST & lCmd:规约命令
	* @param[out]    PRO_FRAME_BODY_LIST & lResult :规约信息体列表
	* @return        int 0-成功 其它失败
	*/
	virtual int _CvtRobertCheckResultToPro(IN PRO_FRAME_BODY_LIST & lCmd,OUT PRO_FRAME_BODY_LIST & lResult);

	/**
	* @brief         根据NX61850读值回应结果及召唤命令生成规约结果列表
	* @param[in]     PRO_FRAME_BODY_LIST & lCmd:规约命令
	* @param[out]    PRO_FRAME_BODY_LIST & lResult :规约信息体列表
	* @return        int 0-成功 其它失败
	*/
	virtual int _Cvt61850ReadResultToPro(IN PRO_FRAME_BODY_LIST & lCmd,OUT PRO_FRAME_BODY_LIST & lResult);
		/**
	* @brief         转换NX通用消息子集数据（值为整型）为组号与通用分类数据集映射表
	* @param[out]     OUT GROUP2GENLIST_MAP & GToGenListMap:通用分类数据集映射表
	* @return        int 0-成功 其它失败
	*/
	virtual int __CvtNxSubFieldToGenList_Int(OUT GROUP2GENLIST_MAP & GToGenListMap);
		/**
	* @brief        机器人 转换NX通用消息子集数据（值为整型）为组号与通用分类数据集映射表
	* @param[out]     OUT GROUP2GENLIST_MAP & GToGenListMap:通用分类数据集映射表
	* @return        int 0-成功 其它失败
	*/
	virtual int __CvtNxSubFieldToGenList_Int_Rot(OUT GROUP2GENLIST_MAP & GToGenListMap);
		/**
	* @brief        61850读值转换NX通用消息子集数据（值为整型）为组号与通用分类数据集映射表
	* @param[out]     OUT GROUP2GENLIST_MAP & GToGenListMap:通用分类数据集映射表
	* @return        int 0-成功 其它失败
	*/
	virtual int __CvtNxSubFieldToGenList_Int_61850Read(OUT GROUP2GENLIST_MAP & GToGenListMap);
		/**
	* @brief         获得硬压板信息点的103配置
	* @param[in]     nID:信息点编号
	* @param[out]    nGroup:组号
	* @param[out]    nItem: 条目号
	* @param[out]    nDataType:数据类型
	* @return        bool:true-成功 false-失败
	*/
	virtual bool ___GetHardDataPoint103Cfg(IN int nID,OUT u_int8 &nGroup,OUT u_int8 &nEntry,OUT u_int8 &nDataType);
	/**
	* @brief         从数据库中查找指定厂站ID.
	* @param[in]     int nIed_Id:设备ID.
	* @param[out]    INT nStation_Id: 厂站ID.
	* @return        int 0-成功 其它失败
	**/
	int __DBQueryRealIed(IN int nIed_Id,IN int nld_code,OUT int & nReal);
	/**
	* @brief         从数据库中查找指定厂站ID.
	* @param[in]     int nIed_Id:设备ID.
	* @param[out]    INT nStation_Id: 厂站ID.
	* @return        int 0-成功 其它失败
	**/
	int __DBQuerySgGroup(IN int nIed_Id,OUT u_int8 &nGroup,OUT u_int8 &nEntry);
	/**
	* @brief         根据ASDU10信息结构格式化ASDU10报文体
	* @param[in]     ASDU10_INFO &Asdu10Info:asdu10信息结构
	* @param[in]     PRO_FRAME_BODY * pCmdBody:命令信息指针(若为NULL,表明该帧转换自动上送信息)
	* @param[out]    PRO_FRAME_BODY_LIST  & lBody :ASDU规约信息体列表
	* @param[in]     int nReserve:备用
	* @return        int 0-成功 其它失败
	*/
	virtual int FormatAsdu10Body(IN ASDU10_INFO &Asdu10Info,OUT PRO_FRAME_BODY_LIST &lBody,IN PRO_FRAME_BODY * pCmdBody=NULL,IN int nReserve=0);
	/**
	* @brief         根据ASDU10信息命令生成失败回应
	* @param[in]     PRO_FRAME_BODY * pCmdBody:命令信息
	* @param[out]    PRO_FRAME_BODY_LIST & lResult :规约信息体列表
	* @return        int 0-成功 其它失败
	*/
	virtual int MakeGenericFailResultByCmd(IN PRO_FRAME_BODY * pCmdBody,OUT PRO_FRAME_BODY_LIST & lResult);
	/**
	* @brief         根据NX事件信息生成规约事件列表
	* @param[in]     NX_EVENT_MESSAGE * pMsg :事件信结构指针
	* @param[out]    PRO_FRAME_BODY_LIST  & lBody :规约信息体
	* @return        int 0-成功 其它失败
	*/
	virtual int ConvertEventMsgToPro(IN NX_EVENT_MESSAGE* pMsg,OUT PRO_FRAME_BODY_LIST & lBody) ;

};


/** @} */ //OVER
#endif // _H_NXECPROASDU10_HZS_H_