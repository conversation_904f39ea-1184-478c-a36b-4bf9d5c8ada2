/**********************************************************************
* ec_pro_srv_fj103_export.cpp         author:ml      date:08/11/2025            
*---------------------------------------------------------------------
*  note: 福建103规约库导出接口实现文件                                                              
*  
**********************************************************************/

#include "ec_pro_srv_def.h"
#include "NXEcFJ103SrvProtocol.h"

/**
* @brief         创建服务端规约实例
* @param[in]     const SRV_PRO_START_PARAM * pParam:服务端规约参数
* @param[out]    无
* @return        INXEcProtocol* ：实例对象指针
*/
INXEcProtocol * CreateSrvProIns(IN const SRV_PRO_START_PARAM * pParam )
{
	CNXEcFJ103SrvProtocol * pProtocol = new CNXEcFJ103SrvProtocol(pParam);
	if( pProtocol != NULL )
	{
		if( !pProtocol->Init() )  // 初始化
		{
			delete pProtocol;
			pProtocol = NULL;
		}
	}
	return pProtocol;
}

/**
* @brief         销毁服务端规约实例
* @param[in]     IN INXEcProtocol * pProIns:规约实例对象指针
* @param[out]    无
* @return        bool : true-成功 false-失败
*/
bool DestroySrvProIns(IN INXEcProtocol * pProIns)
{
	if( pProIns == NULL )
		return true;

	delete (CNXEcFJ103SrvProtocol*)pProIns;
	pProIns = NULL;

	return true;
}