#include "NXEcProXmlHdl.h"


CCvtDataToXml::CCvtDataToXml()
{
	memset(m_cChr, 0, sizeof(m_cChr));
}

CCvtDataToXml::~CCvtDataToXml(void)
{
}
/**
* @brief		初始化			
* @param[in]    
* @return		0-执行成功；其他-执行失败
* @note 
**/
int CCvtDataToXml::Init()
{

	bool bloadlib_gbk2utf8= m_ZclLibmngr_Gbk2Utf8.load_lib();
	if (!bloadlib_gbk2utf8)
	{
		printf("加载动态库libZclGbkToUtf8失败!!!!\n");
		return false;
	}

	//加载配置内容
	if ( 0 >__InitCfgFile())
	{
		printf("加载配置文件内容失败\n");
		return false;
	}

	return 0;
}

/**
* @brief		根据指定的字符或者回车换行符来分隔指定的字符串到字符串队列中。				
* @param[in]	char * cObjStr:将给分隔的字符串
* @param[in]	char flag:分隔标识字符串
* @param[out]	vector<string>:分隔后的list
* @return		0-执行成功；其他-执行失败
* @note 
**/
/***********************************************************************
* @brief     	将汉字由Gbk转换为Utf8格式
* @param[in]    cObj 要转换的字符串
* @return        char *
***********************************************************************/
char * CCvtDataToXml::CvtUtf8ToGbk(const char * cObj)
{
	memset(m_cChr,0,MAX_CHAR_BUFF_LEN);
	m_ZclLibmngr_Gbk2Utf8.convert_utf8_gbk(cObj,m_cChr,MAX_CHAR_BUFF_LEN-1);
	return m_cChr;
}
/***********************************************************************
* @brief     	将汉字由Utf8转换为Gbk格式
* @param[in]    cObj 要转换的字符串
* @return        char *
***********************************************************************/
char * CCvtDataToXml::CvtGbkToUtf8(const char * cObj)
{
	memset(m_cChr,0,MAX_CHAR_BUFF_LEN);
	m_ZclLibmngr_Gbk2Utf8.convert_gbk_utf8(cObj,m_cChr,MAX_CHAR_BUFF_LEN-1);
	return m_cChr;
}

//加载配置文档
int CCvtDataToXml::__InitCfgFile()
{
	
	CIniOperate clMyIni;
	if (!clMyIni.SetIniFileName("Gw103.ini")) 
	{
		printf("[CCvtDataToXml:__InitCfgFile] 没有找到配置文件'Gw103.ini'!.\n");
	}
	else	
	{
		string strTmp;
		clMyIni.ParseIni();
		clMyIni.GetPrivateProfileStr("SYS","file_path","",strTmp);
		
		m_strFilePath = strTmp;

		printf("[CCvtDataToXml:__InitCfgFile] 根据配置文档读到的文件生成路径为:%s.\n",m_strFilePath.c_str());
	}	

	return 0;
}


/**
* @brief		生成短信XML文件.			
* @param[in]    string strXmlFullPath:xml文件生成全路径.
**/
int CCvtDataToXml::MakeDataToXml(ZX2NY_CMD_INFO & cmdInfo,string &strFileName)
{
	char cErr[5555]="";
	MY_TIME_INFO tCur;
	CTimeConvert CCvtTimeCur(time(NULL));
	CCvtTimeCur.GetTimeOfMyTime(tCur);
	
	int nRet = 0;
	TiXmlDocument * pXmlDtl = NULL;
	TiXmlElement * pXmlobjects = NULL;
	TiXmlElement * pXmlSafetyProcess = NULL;
	TiXmlElement * pXmlCreateTest = NULL;
	TiXmlElement * pXmlStartTest = NULL;
	TiXmlElement * pXmlStopTest = NULL;
	TiXmlElement * pXmlTestAllFailedItems = NULL;
	TiXmlElement * pXmlTestItem = NULL;
	TiXmlElement * pXmlTestFrom = NULL;
	TiXmlElement * pXmlCurrentItem = NULL;
	TiXmlElement * pXmlCloseTest = NULL;
	TiXmlElement * pXmldata1 = NULL;
	TiXmlElement * pXmldata2 = NULL;
	TiXmlElement * pXmldata3 = NULL;
	TiXmlElement * pXmldata4 = NULL;

	string strFaultTime;

	char chSysTime[255] = "";

	sprintf(chSysTime,"%04d-%02d-%02d %02d:%02d:%02d",tCur.nYear,tCur.nMon,tCur.nDay,tCur.nHour,tCur.nMin,tCur.nSec);

	pXmlDtl = new TiXmlDocument;

	__MakeXmlFullName(strFileName);// 文件不存在

	TiXmlDeclaration * pDeclaration = new TiXmlDeclaration("1.0","UTF-8","");
	// 增加版本号
	pXmlDtl->LinkEndChild(pDeclaration);

	// 增加TESTCMD节点
	if (NULL == pXmlobjects )
	{
		pXmlobjects = new TiXmlElement("TESTCMD");
		pXmlDtl->LinkEndChild(pXmlobjects);
	}
	// 增加SafetyProcess节点
	if (NULL == pXmlSafetyProcess)
	{
		pXmlSafetyProcess = new TiXmlElement("cmd");
		pXmlSafetyProcess->SetAttribute("id","SafetyProcess");
		pXmlSafetyProcess->SetAttribute("name","SafetyProcess");

		pXmldata1 = new TiXmlElement("data");
		pXmldata1->SetAttribute("name","Dev_Id");
		pXmldata1->SetAttribute("id","Dev_Id");
		pXmldata1->SetAttribute("value",(char *)CvtGbkToUtf8(cmdInfo.SafetyProcessID.c_str()));
		pXmlSafetyProcess->LinkEndChild(pXmldata1);

		pXmldata2 = new TiXmlElement("data");
		pXmldata2->SetAttribute("name","IP");
		pXmldata2->SetAttribute("id","IP");
		pXmldata2->SetAttribute("value",(char *)CvtGbkToUtf8(cmdInfo.SafetyProcessIP.c_str()));
		pXmlSafetyProcess->LinkEndChild(pXmldata2);

		pXmldata3 = new TiXmlElement("data");
		pXmldata3->SetAttribute("name","Current_Value");
		pXmldata3->SetAttribute("id","Current_Value");
		pXmldata3->SetAttribute("value",(char *)CvtGbkToUtf8(cmdInfo.SafetyProcessValue.c_str()));
		pXmlSafetyProcess->LinkEndChild(pXmldata3);

		pXmldata4 = new TiXmlElement("data");
		pXmldata4->SetAttribute("name","Cmd_Time");
		pXmldata4->SetAttribute("id","Cmd_Time");
		pXmldata4->SetAttribute("value",(char *)CvtGbkToUtf8(cmdInfo.SafetyProcessTime.c_str()));
		pXmlSafetyProcess->LinkEndChild(pXmldata4);

		pXmlobjects->LinkEndChild(pXmlSafetyProcess);
	}
	// 增加CreateTest节点
	if (NULL == pXmlCreateTest)
	{
		pXmlCreateTest = new TiXmlElement("cmd");
		pXmlCreateTest->SetAttribute("id","CreateTest");
		pXmlCreateTest->SetAttribute("name","CreateTest");

		pXmldata1 = new TiXmlElement("data");
		pXmldata1->SetAttribute("name","Dev_Id");
		pXmldata1->SetAttribute("id","Dev_Id");
		pXmldata1->SetAttribute("value",(char *)CvtGbkToUtf8(cmdInfo.CreateTestID.c_str()));
		pXmlCreateTest->LinkEndChild(pXmldata1);

		pXmldata2 = new TiXmlElement("data");
		pXmldata2->SetAttribute("name","IP");
		pXmldata2->SetAttribute("id","IP");
		pXmldata2->SetAttribute("value",(char *)CvtGbkToUtf8(cmdInfo.CreateIP.c_str()));
		pXmlCreateTest->LinkEndChild(pXmldata2);

		pXmldata3 = new TiXmlElement("data");
		pXmldata3->SetAttribute("name","Current_Value");
		pXmldata3->SetAttribute("id","Current_Value");
		pXmldata3->SetAttribute("value",(char *)CvtGbkToUtf8(cmdInfo.CreateTestValue.c_str()));
		pXmlCreateTest->LinkEndChild(pXmldata3);

		pXmldata4 = new TiXmlElement("data");
		pXmldata4->SetAttribute("name","Cmd_Time");
		pXmldata4->SetAttribute("id","Cmd_Time");
		pXmldata4->SetAttribute("value",(char *)CvtGbkToUtf8(cmdInfo.CreateTestTime.c_str()));
		pXmlCreateTest->LinkEndChild(pXmldata4);

		pXmlobjects->LinkEndChild(pXmlCreateTest);
	}
	// 增加StartTest节点
	if (NULL == pXmlStartTest)
	{
		pXmlStartTest = new TiXmlElement("cmd");
		pXmlStartTest->SetAttribute("id","StartTest");
		pXmlStartTest->SetAttribute("name","StartTest");

		pXmldata3 = new TiXmlElement("data");
		pXmldata3->SetAttribute("name","Current_Value");
		pXmldata3->SetAttribute("id","Current_Value");
		pXmldata3->SetAttribute("value",(char *)CvtGbkToUtf8(cmdInfo.StartTestValue.c_str()));
		pXmlStartTest->LinkEndChild(pXmldata3);

		pXmldata4 = new TiXmlElement("data");
		pXmldata4->SetAttribute("name","Cmd_Time");
		pXmldata4->SetAttribute("id","Cmd_Time");
		pXmldata4->SetAttribute("value",(char *)CvtGbkToUtf8(cmdInfo.StartTestTime.c_str()));
		pXmlStartTest->LinkEndChild(pXmldata4);

		pXmlobjects->LinkEndChild(pXmlStartTest);
	}
	// 增加StopTest节点
	if (NULL == pXmlStopTest)
	{
		pXmlStopTest = new TiXmlElement("cmd");
		pXmlStopTest->SetAttribute("id","StopTest");
		pXmlStopTest->SetAttribute("name","StopTest");

		pXmldata3 = new TiXmlElement("data");
		pXmldata3->SetAttribute("name","Current_Value");
		pXmldata3->SetAttribute("id","Current_Value");
		pXmldata3->SetAttribute("value",(char *)CvtGbkToUtf8(cmdInfo.StopTestValue.c_str()));
		pXmlStopTest->LinkEndChild(pXmldata3);

		pXmldata4 = new TiXmlElement("data");
		pXmldata4->SetAttribute("name","Cmd_Time");
		pXmldata4->SetAttribute("id","Cmd_Time");
		pXmldata4->SetAttribute("value",(char *)CvtGbkToUtf8(cmdInfo.StopTestTime.c_str()));
		pXmlStopTest->LinkEndChild(pXmldata4);

		pXmlobjects->LinkEndChild(pXmlStopTest);
	}
	// 增加TestAllFailedItems节点
	if (NULL == pXmlTestAllFailedItems)
	{
		pXmlTestAllFailedItems = new TiXmlElement("cmd");
		pXmlTestAllFailedItems->SetAttribute("id","TestAllFailedItems");
		pXmlTestAllFailedItems->SetAttribute("name","TestAllFailedItems");

		pXmldata3 = new TiXmlElement("data");
		pXmldata3->SetAttribute("name","Current_Value");
		pXmldata3->SetAttribute("id","Current_Value");
		pXmldata3->SetAttribute("value",(char *)CvtGbkToUtf8(cmdInfo.TestAllFailedItemsValue.c_str()));
		pXmlTestAllFailedItems->LinkEndChild(pXmldata3);

		pXmldata4 = new TiXmlElement("data");
		pXmldata4->SetAttribute("name","Cmd_Time");
		pXmldata4->SetAttribute("id","Cmd_Time");
		pXmldata4->SetAttribute("value",(char *)CvtGbkToUtf8(cmdInfo.TestAllFailedItemsTime.c_str()));
		pXmlTestAllFailedItems->LinkEndChild(pXmldata4);

		pXmlobjects->LinkEndChild(pXmlTestAllFailedItems);
	}
	// 增加TestItem节点
	if (NULL == pXmlTestItem)
	{
		pXmlTestItem = new TiXmlElement("cmd");
		pXmlTestItem->SetAttribute("id","TestItem");
		pXmlTestItem->SetAttribute("name","TestItem");

		pXmldata1 = new TiXmlElement("data");
		pXmldata1->SetAttribute("name","Current_Value");
		pXmldata1->SetAttribute("id","Current_Value");
		pXmldata1->SetAttribute("value",(char *)CvtGbkToUtf8(cmdInfo.TestItemValue.c_str()));
		pXmlTestItem->LinkEndChild(pXmldata1);

		pXmldata2 = new TiXmlElement("data");
		pXmldata2->SetAttribute("name","ItemPath");
		pXmldata2->SetAttribute("id","ItemPath");
		pXmldata2->SetAttribute("value",(char *)CvtGbkToUtf8(cmdInfo.TestItemID.c_str()));
		pXmlTestItem->LinkEndChild(pXmldata2);

		pXmldata3 = new TiXmlElement("data");
		pXmldata3->SetAttribute("name","Cmd_Time");
		pXmldata3->SetAttribute("id","Cmd_Time");
		pXmldata3->SetAttribute("value",(char *)CvtGbkToUtf8(cmdInfo.TestItemTime.c_str()));
		pXmlTestItem->LinkEndChild(pXmldata3);

		pXmlobjects->LinkEndChild(pXmlTestItem);
	}
	// 增加TestFrom节点
	if (NULL == pXmlTestFrom)
	{
		pXmlTestFrom = new TiXmlElement("cmd");
		pXmlTestFrom->SetAttribute("id","TestFrom");
		pXmlTestFrom->SetAttribute("name","TestFrom");

		pXmldata1 = new TiXmlElement("data");
		pXmldata1->SetAttribute("name","Current_Value");
		pXmldata1->SetAttribute("id","Current_Value");
		pXmldata1->SetAttribute("value",(char *)CvtGbkToUtf8(cmdInfo.TestFromValue.c_str()));
		pXmlTestFrom->LinkEndChild(pXmldata1);

		pXmldata2 = new TiXmlElement("data");
		pXmldata2->SetAttribute("name","ItemPath");
		pXmldata2->SetAttribute("id","ItemPath");
		pXmldata2->SetAttribute("value",(char *)CvtGbkToUtf8(cmdInfo.TestFromID.c_str()));
		pXmlTestFrom->LinkEndChild(pXmldata2);

		pXmldata3 = new TiXmlElement("data");
		pXmldata3->SetAttribute("name","Cmd_Time");
		pXmldata3->SetAttribute("id","Cmd_Time");
		pXmldata3->SetAttribute("value",(char *)CvtGbkToUtf8(cmdInfo.TestItemTime.c_str()));
		pXmlTestFrom->LinkEndChild(pXmldata3);

		pXmlobjects->LinkEndChild(pXmlTestFrom);
	}
	// 增加CloseTest节点
	if (NULL == pXmlCloseTest)
	{
		pXmlCloseTest = new TiXmlElement("cmd");
		pXmlCloseTest->SetAttribute("id","CloseTest");
		pXmlCloseTest->SetAttribute("name","CloseTest");

		pXmldata1 = new TiXmlElement("data");
		pXmldata1->SetAttribute("name","Current_Value");
		pXmldata1->SetAttribute("id","Current_Value");
		pXmldata1->SetAttribute("value",(char *)CvtGbkToUtf8(cmdInfo.CloseTestItemValue.c_str()));
		pXmlCloseTest->LinkEndChild(pXmldata1);

		pXmldata2 = new TiXmlElement("data");
		pXmldata2->SetAttribute("name","Cmd_Time");
		pXmldata2->SetAttribute("id","Cmd_Time");
		pXmldata2->SetAttribute("value",(char *)CvtGbkToUtf8(cmdInfo.CloseTestItemTime.c_str()));
		pXmlCloseTest->LinkEndChild(pXmldata2);

		pXmlobjects->LinkEndChild(pXmlCloseTest);
	}

	// 保存
	pXmlDtl->SaveFile(strFileName.c_str());

	printf("文件%s ,保存成功！\n",strFileName.c_str());

	pXmlDtl = NULL;
	delete pXmlDtl;
	

	return nRet;
}

/**
* @brief		做成报告存放的路径+文件名(路径不存在则创建).			
* @param[in]    string & strObjFullName:结果字符串.
* @return		0-执行成功；其他-执行失败
* @note 
**/
int CCvtDataToXml::__MakeXmlFullName(string & strObjFullName)
{
// 	string strRootPath = CvtUtf8ToGbk((char*)m_strFilePath.c_str());
// 	strObjFullName = strRootPath + "/ATS_TEST_CMD.xml";

	strObjFullName = m_strFilePath + "/ATS_TEST_CMD.xml";

	printf("[20200615]全文件名=%s \n",strObjFullName.c_str());


	if ( 0 != sy_dirfile_exist(strObjFullName.c_str()) )
	{
		if (!sy_create_dir(const_cast<char *>(m_strFilePath.c_str())))
		{
			printf("文件路径[%s]创建失败!",m_strFilePath.c_str());
			return -1;
		}
	}

	printf("[CCvtDataToXml::__MakeXmlFullName]m_strFilePath为:%s,strObjFullName为:%s \n",m_strFilePath.c_str(),strObjFullName.c_str()) ;

	return 0;
}
