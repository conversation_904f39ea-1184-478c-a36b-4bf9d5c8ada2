/**********************************************************************
* NXEcProAsdu7_FJ.h         author:ml      date:08/11/2025            
*---------------------------------------------------------------------
*  note: ASDU7报文转换处理头文件                                                                 
*  
**********************************************************************/

#ifndef _H_NXECPROASDU7_FJ_H_ 
#define _H_NXECPROASDU7_FJ_H_

#include "NXEcProAsdu7.h"

/**
* @defgroup  福建104 TNXEcProAsdu7DB:ASDU7报文转换处理结点类
* @{
*/
 

class TNXEcProAsdu7FJ:public TNXEcProAsdu7
{
	///////////////////////////////////////////////////////////////构造、析构
public:
	
	/**
	* @brief         析构函数
	* @param[in]     无 
	* @param[out]    无
	* @return        无
	*/
	virtual ~TNXEcProAsdu7FJ();

    /**
	* @brief         构造函数 
	* @param[in]     INXEcSSModelSeek * pSeekIns:模型查询实例
	* @param[out]    CLogRecord * pLogRecord:日志对象指针
	* @return        无
	*/
	TNXEcProAsdu7FJ(INXEcSSModelSeek * pSeekIns,CLogRecord * pLogRecord);

	///////////////////////////////////////////////////////////////公用方法
public:
	/**
	* @brief         直接从本地生成结果回应，如初始化配置;
	* @param[in]     PRO_FRAME_BODY * pBody :规约信息体指针
	* @param[out]    PRO_FRAME_BODY_LIST & lResult：本地生成的结果帧体列表
	* @return        int 0-成功 其它失败
	*/
	int DirectResFromLocal(PRO_FRAME_BODY * pBody,PRO_FRAME_BODY_LIST & lResult) ;

		////////////////////////////////////////////////////////////////////////保护方法
protected:
	/**
	* @brief		响应全站总招，生成已接入设备的信息列表:fun 统一为255,103地址(inf),dpi 最新通信状态			
	* @param[out]   INFO_OBJ_LIST& lInfoObj:返回信息列表
	* @return		0-执行成功；其他-执行失败
	**/
	int MakeAsdu7InfoList_runstatus(INFO_OBJ_LIST& lInfoObj);

	/**
	* @brief		生成已接入设备定值区信息列表:fun=250,103地址(inf),dpi=当前定值区号			
	* @param[out]   INFO_OBJ_LIST& lInfoObj:返回信息列表
	* @return		0-执行成功；其他-执行失败
	**/
	int MakeAsdu7InfoList_settinggroup(INFO_OBJ_LIST& lInfoObj);

		/**
	* @brief         转换规约信息到NX通用消息结构
	* @param[in]     PRO_FRAME_BODY* pCmd:规约命令
	* @param[out]    NX_COMMON_MSG_LIST & lMsg: 保存生成的通用消息列表
	* @param[out]    PRO_FRAME_BODY_LIST & lBody：保存生成的规约失败回应(服务端规约有效）
	* @return        >=0:成功 <0:失败
	*/
	virtual int _CvtOneCmdToCommonMsg(PRO_FRAME_BODY* pCmd,NX_COMMON_MSG_LIST & lMsg,PRO_FRAME_BODY_LIST & lBody);

	/**
	* @brief         从数据库中查找所有厂站的通信状态和运行状态.
	* @param[out]    LIST_IED &ListIed: 二次设备IED基本信息表字段链表.
	* @return        int 0-成功 其它失败
	**/
	int __DBQueryGroupName(int ied_obj,int ld_code);
private:
	/**
	* @brief         从数据库中查找指定厂站ID.
	* @param[in]     int nIed_Id:设备ID.
	* @param[out]    INT nStation_Id: 厂站ID.
	* @return        int 0-成功 其它失败
	**/
	int __DBQueryRealIed(int nIed_Id,int nld_code,int & nReal);
	/**
	* @brief         根据开关量从数据库中查找指定设备ID.
	* @param[in]     int nIed_Id:设备ID.
	* @param[out]    INT nStation_Id: 厂站ID.
	* @return        int 0-成功 其它失败
	**/
	int __DBQueryRealIedByBack(int &obj_id,int &n_field_id,int  nReal);

};


/** @} */ //OVER


#endif  // _H_NXECPROASDU7_FJ_H_