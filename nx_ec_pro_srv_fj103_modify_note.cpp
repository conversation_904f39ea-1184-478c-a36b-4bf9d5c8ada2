﻿/*********************************************************************
*ec_srv_fj103_modify_note.cpp       creater:ml    create date:20/08/2025
*-------------------------------------------------------------
*               note: 福建103服务端规约转换模块历史修改记录
*  
*********************************************************************/

/**  Ver 1.0.0 2025-08-20 修改人：马梁
1、修改内容:
(1)基础功能代码由gw104复制而来；
(2)删除gw104中新疆特定的功能代码，主要为NXEcProAsdu10_FJ类和NXEcProAsdu101_FJ类；
(1)在ASDU7类中响应总召上送的消息中增加子站向主站上送装置的当前定值区号，功能类型为250（FAH），信息序号为装置地址，DPI为装置的当前定值区；
(2)在ASDU10类中通过ASDU10报文将当前运行定值区变化信息，与故障量信息通过“返回信息标识符(RII)”进行区分，故障量信息帧的返回信息标识符填253（FDH），当前运行定值区变化信息帧的返回信息标识符填254（FEH）；
(3)在ASDU17类中扩展历史事件召唤信息，增加软压板信息与通信信息；
(4)在ASDU20类中新增远方复归二次设备指示灯功能，支持远方控制并返回结果；
2. 影响范围： 
(1)子站总召回复；
(2)当前运行定值区变化信息，与故障量信息回复报文中的nRii区分；
(3)历史信息召唤增加软压板信息与通信信息;
(4)新增远方复归二次设备指示灯功能；
3.代码修改范围：
本次共修改11个文件：NXEcProAsdu7_FJ.h、NXEcProAsdu7_FJ.cpp、NXEcProAsdu10_FJ.h、NXEcProAsdu10_FJ.cpp、NXEcProAsdu17_FJ.h、NXEcProAsdu17_FJ.cpp、NXEcProAsdu20_FJ.h、NXEcProAsdu20_FJ.cpp、NXEcProAsdu101_FJ.h、NXEcProAsdu101_FJ.cpp、makefile
**/